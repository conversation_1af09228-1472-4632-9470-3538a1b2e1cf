<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		VNDijZhTTPX/KVc/lI5p1OnhMjw=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		sL29kg0FRS9L5NMR9OZ8jvH2qTU=
		</data>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<data>
		2s8gAPl2YSEHZTndG9fGXuATSss=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/App.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			bnH5J1Sts13vV15zvqD1NQO43q4=
			</data>
			<key>requirement</key>
			<string>cdhash H"6e71f92754adb35def575e73bea0f53503b8deae" or cdhash H"cf2ac6d362239a2e87043bb7caca5ba5db2d6e93"</string>
		</dict>
		<key>Frameworks/FlutterMacOS.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			nvnlfNHi9KnmAR8oOPY0ocdfb/4=
			</data>
			<key>requirement</key>
			<string>cdhash H"9ef9e57cd1e2f4a9e6011f2838f634a1c75f6ffe" or cdhash H"8cf7f7a93c519f538d701b67a3810784166b0b91"</string>
		</dict>
		<key>Frameworks/OrderedSet.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			m9mO/Kj7wX/VFsEBetn/BSosves=
			</data>
			<key>requirement</key>
			<string>cdhash H"9bd98efca8fbc17fd516c1017ad9ff052a2cbdeb" or cdhash H"64091cadb01465fb22459d44b3cfc82271cf8208"</string>
		</dict>
		<key>Frameworks/app_links.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			+4fp3nPLoTOKHA230mKr5+Reovo=
			</data>
			<key>requirement</key>
			<string>cdhash H"fb87e9de73cba1338a1c0db7d262abe7e45ea2fa" or cdhash H"adbe6a296de679ab29faf8952bd55c60872c2b81"</string>
		</dict>
		<key>Frameworks/audio_session.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			q2hERs5wjIay7KxySU4nSLRYqi8=
			</data>
			<key>requirement</key>
			<string>cdhash H"ab684446ce708c86b2ecac72494e2748b458aa2f" or cdhash H"b4b3ccd924468b437637cf58db9cb83b908b028a"</string>
		</dict>
		<key>Frameworks/desktop_drop.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			QGuoQgO/lvktTyaqQPYcvc+NRpI=
			</data>
			<key>requirement</key>
			<string>cdhash H"406ba84203bf96f92d4f26aa40f61cbdcf8d4692" or cdhash H"42c69d5682c6905f5fa32f3876b70aa492b7061f"</string>
		</dict>
		<key>Frameworks/file_picker.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			ShbG6ewAvhGnx5yRuw2HGkZrfCE=
			</data>
			<key>requirement</key>
			<string>cdhash H"4a16c6e9ec00be11a7c79c91bb0d871a466b7c21" or cdhash H"55c7d49df8cc9b56e936cb9c3ad98caad48f8329"</string>
		</dict>
		<key>Frameworks/file_selector_macos.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			H46XDUzSBBTIUE2ryevUaO2u0mA=
			</data>
			<key>requirement</key>
			<string>cdhash H"1f8e970d4cd20414c8504dabc9ebd468edaed260" or cdhash H"51d8e3029198de18fe1edd12a1fd87b9c94c66d5"</string>
		</dict>
		<key>Frameworks/flutter_inappwebview_macos.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			03oXJbtX0USFWE8scJ8HWGxzlRk=
			</data>
			<key>requirement</key>
			<string>cdhash H"d37a1725bb57d14485584f2c709f07586c739519" or cdhash H"a19072f3a8271722e5984a360fef1c49c460d939"</string>
		</dict>
		<key>Frameworks/flutter_secure_storage_darwin.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			BcLNjbJLkfLNhXUGKEB2goiSw3w=
			</data>
			<key>requirement</key>
			<string>cdhash H"05c2cd8db24b91f2cd857506284076828892c37c" or cdhash H"4defda4ff50d1f4acb8fe0c9408b995c869270ed"</string>
		</dict>
		<key>Frameworks/just_audio.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			ZfeoaQ0Kl/kbSgYFZrmoHhoUPy8=
			</data>
			<key>requirement</key>
			<string>cdhash H"65f7a8690d0a97f91b4a060566b9a81e1a143f2f" or cdhash H"09e1b93a998058e793e948c22f43b4d4d1188689"</string>
		</dict>
		<key>Frameworks/mobile_scanner.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			0eJ0PXi+o2YvBW8g/8jIpZeZ3RA=
			</data>
			<key>requirement</key>
			<string>cdhash H"d1e2743d78bea3662f056f20ffc8c8a59799dd10" or cdhash H"719240f79f704aa05f178237288f836e96666337"</string>
		</dict>
		<key>Frameworks/package_info_plus.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			s8FEAhAGwDWCMJ+EjdL/T5TX5P4=
			</data>
			<key>requirement</key>
			<string>cdhash H"b3c144021006c03582309f848dd2ff4f94d7e4fe" or cdhash H"4025065bb8b3c842366f20dee778bab1da4648d9"</string>
		</dict>
		<key>Frameworks/path_provider_foundation.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			MLu605s9aeKRyeMnwsAEYwjB6rg=
			</data>
			<key>requirement</key>
			<string>cdhash H"30bbbad39b3d69e291c9e327c2c0046308c1eab8" or cdhash H"67cd30cc07fe92bfad999df1305ae96b624bcaaf"</string>
		</dict>
		<key>Frameworks/record_macos.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			bMSo8aqSOhKkX97dzA/koFImmxI=
			</data>
			<key>requirement</key>
			<string>cdhash H"6cc4a8f1aa923a12a45fdeddcc0fe4a052269b12" or cdhash H"b0bbf3cdb9cb47ce4e27a07fe54d706ed585831b"</string>
		</dict>
		<key>Frameworks/share_plus.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			PSVtxF6q5bubsq+7lO6n8uKkqsc=
			</data>
			<key>requirement</key>
			<string>cdhash H"3d256dc45eaae5bb9bb2afbb94eea7f2e2a4aac7" or cdhash H"c748734e267f320b3d994f25a992e11dc0fa32cf"</string>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			G3QmcHbNrzSVzEKmYYWLpYFGPNU=
			</data>
			<key>requirement</key>
			<string>cdhash H"1b74267076cdaf3495cc42a661858ba581463cd5" or cdhash H"5f6baf98282b41ecdaa50f234b1cc88c4b1cc1fe"</string>
		</dict>
		<key>Frameworks/url_launcher_macos.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			cJKnpWg2XhdBixqhbwYhm6DJ/1I=
			</data>
			<key>requirement</key>
			<string>cdhash H"7092a7a568365e17418b1aa16f06219ba0c9ff52" or cdhash H"bdd3992ff5023ba16ac455a39d49eea8966d07e9"</string>
		</dict>
		<key>Frameworks/video_player_avfoundation.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			ffXEuCK9PBZ5dvyPiYrwK4sMrJk=
			</data>
			<key>requirement</key>
			<string>cdhash H"7df5c4b822bd3c167976fc8f898af02b8b0cac99" or cdhash H"c8112ecca512ae7346a230cdf6c48ba5f6959133"</string>
		</dict>
		<key>Frameworks/wakelock_plus.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			JI2JXD/YGdnm6sIJ+bh0p3Oz2I8=
			</data>
			<key>requirement</key>
			<string>cdhash H"248d895c3fd819d9e6eac209f9b874a773b3d88f" or cdhash H"e263ba0ab008ad53895c8b63929348d3a0cfb83d"</string>
		</dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			mh/nPDOKKYD2TepW5mnKYp7LZaTcxZfEBTPJjFwzeak=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			kkVQEy6HXtVX34kMtNLfYaNG0y9SebmB2iwsHZRAOFU=
			</data>
		</dict>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			vYurBikNjNQ7g88MzNBiD7ABlbocZiS18ZJucv/VZnU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
