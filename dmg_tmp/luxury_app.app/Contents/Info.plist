<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>24F74</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>luxury_app</string>
	<key>CFBundleIconFile</key>
	<string>AppIcon</string>
	<key>CFBundleIconName</key>
	<string>AppIcon</string>
	<key>CFBundleIdentifier</key>
	<string>com.example.chatApp</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>LuxuryApp</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.1.0</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>1.1.0</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string>24F74</string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>15.5</string>
	<key>DTSDKBuild</key>
	<string>24F74</string>
	<key>DTSDKName</key>
	<string>macosx15.5</string>
	<key>DTXcode</key>
	<string>1640</string>
	<key>DTXcodeBuild</key>
	<string>16F6</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.15</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>Для создания фотографий и отправки их в чат требуется доступ к камере.</string>
	<key>NSDesktopFolderUsageDescription</key>
	<string>Для выбора файлов с рабочего стола требуется доступ к папке.</string>
	<key>NSDocumentsFolderUsageDescription</key>
	<string>Для выбора документов требуется доступ к папке документов.</string>
	<key>NSDownloadsFolderUsageDescription</key>
	<string>Для выбора файлов из загрузок требуется доступ к папке.</string>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright 2025 com.example. All rights reserved.</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Для записи голосовых сообщений и работы с ИИ требуется доступ к микрофону.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Для выбора изображений из галереи требуется доступ к медиатеке.</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
</dict>
</plist>
