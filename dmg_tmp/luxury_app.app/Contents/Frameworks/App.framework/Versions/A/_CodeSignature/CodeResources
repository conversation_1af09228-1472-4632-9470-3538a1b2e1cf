<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		7zfVkem4L9eyF/Y1+BqaGLsXgNk=
		</data>
		<key>Resources/flutter_assets/.env</key>
		<data>
		KQP4Sd+sPlevS8Q2rJQ2iWQlBHo=
		</data>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<data>
		V+bXdrligV1yM3FFWwoVWxa54DY=
		</data>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<data>
		yopdSg9RpNNX2/2tJ5iWBSCPq1A=
		</data>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<data>
		pavbGXszPmkyuIxxRSNchdtj7D4=
		</data>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<data>
		w/6k3ZcNURQvGh1S4f7wbxdzEU4=
		</data>
		<key>Resources/flutter_assets/NativeAssetsManifest.json</key>
		<data>
		re4p7E8rPLLsN+wzaPN/+AVpXTY=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Black.ttf</key>
		<data>
		RViCBlEYMo88Q1PQFdkC9YuIGds=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Bold.ttf</key>
		<data>
		YEj6JCwlibk27nYRIJxULNXJL3o=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-ExtraBold.ttf</key>
		<data>
		/iqpMZqvk+p5qmEiDfhQTJFjinA=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-ExtraLight.ttf</key>
		<data>
		po9qIQKI5czigpd2SRR2mDlMp2s=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Light.ttf</key>
		<data>
		XmO5Mz5ZZdLIjkWECecDy1Xwtg4=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Medium.ttf</key>
		<data>
		N4xORU0GTLbzEiDJHn/v6L036h8=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Regular.ttf</key>
		<data>
		CVWC+1rQygLUY7ORSi13iiGr0oU=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-SemiBold.ttf</key>
		<data>
		wYaOxSyutuFkd2rxAcJvfVt+g3c=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Thin.ttf</key>
		<data>
		MpBToqKzOMwXsPRjsm2/GYCJWZM=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica-VariableFont_CRSV,SHRP,slnt,wght.ttf</key>
		<data>
		34WU7q8+VAITealizJxf/5tmuHk=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Black.ttf</key>
		<data>
		ngmhB+h7KKVzVebudewo96Lugd0=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Bold.ttf</key>
		<data>
		eskINoIFG2WiwdskeLSbm8tOcxg=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-ExtraBold.ttf</key>
		<data>
		bU+VB1lmEJuzFw7P4RKPJxZ4OjM=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-ExtraLight.ttf</key>
		<data>
		dgz93s6PvVAZWMFRXAkiZyv0jhY=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Light.ttf</key>
		<data>
		SI67gIrzYL4mg/vYHF6FqYQkf8Y=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Medium.ttf</key>
		<data>
		vCPGGZEFa0I0IcVbE+PTEfeGXMY=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Regular.ttf</key>
		<data>
		LAo4a9vmiT6KEel+7eU9tLoRemo=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-SemiBold.ttf</key>
		<data>
		L9YWpk3HUYHojXrrgdHEVC696es=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Thin.ttf</key>
		<data>
		uYSFQ2QJDDpCO9I/sXYuzCxCYCc=
		</data>
		<key>Resources/flutter_assets/assets/fonts/JetBrainsMono-Bold.ttf</key>
		<data>
		R/P/+S+h7mq11a+K8QqtUZTU0+k=
		</data>
		<key>Resources/flutter_assets/assets/fonts/JetBrainsMono-Regular.ttf</key>
		<data>
		LsImTwp1mE+4O6OFPSbamXGyWlU=
		</data>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Bold.ttf</key>
		<data>
		jnwYBPXJen6mJFnSKCcpp7GO76Y=
		</data>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Light.ttf</key>
		<data>
		IaGQAYmEd/BoWqeVM+LA+QI2cdA=
		</data>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Regular.ttf</key>
		<data>
		4yULhu5RZTo1nvZPbFjvD67Kid8=
		</data>
		<key>Resources/flutter_assets/assets/fonts/NunitoSans-Variable.ttf</key>
		<data>
		2zm7NZ2BYvpzYmqIXi7JCdSlccE=
		</data>
		<key>Resources/flutter_assets/assets/fonts/PTMono-Regular.ttf</key>
		<data>
		rev91JHLO/LruS6iPp7B7BYI+bw=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Bold.ttf</key>
		<data>
		emCHpOIsj0BX9iFPzDfGXIRmD24=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Medium.ttf</key>
		<data>
		EGQwxxq9Tivq0f6rCP9RYyEd6Co=
		</data>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Regular.ttf</key>
		<data>
		BuOvDIKWhauGWcAMFthRGGo21vo=
		</data>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		ecYubhq8ooh9DZzFaIPs50SQP04=
		</data>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Xk+j/Hwvg2CLrzviMnrLEMFh8vU=
		</data>
		<key>Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css</key>
		<data>
		mBihw0DQ+fObskdvU/uJxi4GQ/4=
		</data>
		<key>Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html</key>
		<data>
		60RUAYYoUnETCwVvpgmbGYgxn8Q=
		</data>
		<key>Resources/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js</key>
		<data>
		C4gP48yB0LzK3THcrd7cWSW2bAU=
		</data>
		<key>Resources/flutter_assets/packages/lucide_icons/assets/lucide.ttf</key>
		<data>
		MW2J5dMAar95VOjj9bj4EIO+ofM=
		</data>
		<key>Resources/flutter_assets/packages/record_web/assets/js/record.fixwebmduration.js</key>
		<data>
		jdOHZCknGz0mLf/bJm5RUxN05CI=
		</data>
		<key>Resources/flutter_assets/packages/record_web/assets/js/record.worklet.js</key>
		<data>
		PFvhnXQ/UnHMZZ1Wyk6xuN6QBH4=
		</data>
		<key>Resources/flutter_assets/packages/wakelock_plus/assets/no_sleep.js</key>
		<data>
		4X7PZ95hkgUE15GU2+5c1VKgHP0=
		</data>
		<key>Resources/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp</key>
		<data>
		TDvhTCHVurikSmzXUIYz0DM62Dw=
		</data>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		VvTF10G1gIeea4aI0DhJjCjHgXQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			CLA7WOT/JcZvXmAZOycoq1JGVjvE8lMhGlxINbvB0zM=
			</data>
		</dict>
		<key>Resources/flutter_assets/.env</key>
		<dict>
			<key>hash2</key>
			<data>
			cC2MU59Wd797Ru0u3fWGTkH4Es/RX80xvgqjjNG86U8=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			0/ITxBx2nq//d58dEPb1h6TSB/k6u2hed+krEucyD0g=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			swMW3ZyT3k9X3BtUBKv8Vf7rNWvgSYRrnNp870Hd52c=
			</data>
		</dict>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			p9AWIVaoDt4hv3Hwfom6CHcHZ5QHK4UGpJiXnlEWLIA=
			</data>
		</dict>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			ob7mGKZyAau5279XhPdf1nWET2lXdaerfgUl+ejzkc0=
			</data>
		</dict>
		<key>Resources/flutter_assets/NativeAssetsManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUijHkoEgTXB2U+Rkyi/tirix7s8q5ZVfHlB2ql3dss=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Black.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			KacaTQPTDkmuXcjaZT2xFjwyJXDruOVRrMoVm4dTit0=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			hMZPPVEvgjG2FAxjZe5qbqZfoDKbATjdf48VnSchLfM=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-ExtraBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			CMaM0zSdZYGwxGHoUxusv/WChx15VaHUSr4HfVLbAME=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-ExtraLight.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			am1rYzxk0qB7PFBhSB4p/HGOZO7nSDfm9wZ/ln/+vgk=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			4ZuQtVqSzhZcvMbzPjynx0GYBljTKpeL9aI8awQrfDU=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			gs9f1uqb1fApBfrrmJ20RtWnllDzRDuNqvwgJs6XNMM=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			dIrmYI9+d9nqc0nJI0hY4Y59DAtOmzl/1vTd7PwtZmM=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-SemiBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			H9ptc0skdZdqWzgW0MtRR7CHo1MNtP7O3ZspHUkyr/A=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-Thin.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6xH40XRJzDKleiTrGMgU+J/0JFsDTMEzWwd0hj0OhQg=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica-VariableFont_CRSV,SHRP,slnt,wght.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			OfqNnFjbmFzo8DcHxnlwyhcTIbexEnZAV1acmqzeYls=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Black.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Yr8yg6UGlpj6MT6PaoCKjBEa4HpXZijatWF6DmMgRYQ=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			4M5BkAFTDcGIFB3JaA0sw32Uzn1Kfw71KqNIJW//YZg=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-ExtraBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			sU0ew4EHh3w1QOtfdiT6cT2ZuEdL8JhaxVUpKznlsSc=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-ExtraLight.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			X5FG0hj6EIPFE18XJDFc+YxEF40pOKQHloEbFrsAAh4=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Ssjzb4bR3lQxEwYgH1ef+0OWAwmRXv4pj4/0JDEDu3Y=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			vYC5wGFiUerHyDFlxjIPDuzsH1aDOyanyvJ5zLlr3o4=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6DEa2TlQoxze764enbUWa5viwmYwP2oN8bONXBqTiT8=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-SemiBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			SWKjMPbjEbwsoTnHk1KakFNmacJmU4eXfnTIdpND3Tw=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Geologica_Cursive-Thin.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			3WoHZS0rsmYNBcZQ0RLyboQlTOXwKCdegqHwCPk/ivs=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/JetBrainsMono-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			0ixPOCHXJesBIQ0njZXfz8qtw0aZoGZY1HyKXMWDCto=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/JetBrainsMono-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			5v0NfpFVCz7StzXUMSR0NixHFu3E/AV3oPYe14LVrtE=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			5ws7aKtOhNcfe4ROyqKQGegTnwzyDsYrbR+ghwdYMnI=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			i0kCmT0EpojNOUuU/NwAOSsb2vMb3V5yjNZ/tfGfWRk=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/NotoSans-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			PSMyo1qOdmzLy+qyo7YhBRWVtpKSotw1MRWLt+kad+s=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/NunitoSans-Variable.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			JrDBqq0r3Hvq0MFRoAfsKcgDqz0/vtN14GogV3avtgs=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/PTMono-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			UbmEeBGbGD2paNFe0XL50M5JHh+J8NYrGqIwusNdy0Y=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			1+c5ohWaVFIPklK/W+6Ufe0oGyFvh/7rgs/ytC2PREs=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			17dEUDRljGh7nUXCNGVrdAclhgw7gyt5H3OLcHn158o=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/fonts/Roboto-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			xRrhN5lwiv3x+wWF1YiPsN8VOUJw1t7yUpjerpsh2ik=
			</data>
		</dict>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			a+2EfkeGQGpDpOsYBy4p/4ooiSbGU4rwViWylP+wDYo=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			WIh+5gYxxAZwqox/bJy0upd1c01Dhah86e/UEz/c6X4=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css</key>
		<dict>
			<key>hash2</key>
			<data>
			LlYyIL2FXhHyMNwnoNby/VuDmimW+G7cgCWqtF5vpcM=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html</key>
		<dict>
			<key>hash2</key>
			<data>
			3HLPwfHSpQE7ud40+MrPXiblQtfXE/y+CbhltKrKbd8=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/flutter_inappwebview_web/assets/web/web_support.js</key>
		<dict>
			<key>hash2</key>
			<data>
			L5zO+dtNprc9PGeP/lcKrUAnkbAN+lIHMP22bQ3CCfw=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/lucide_icons/assets/lucide.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			lQW5vm8pauGWTmEJH0788zvI6rahQnkhVoNy+JBPkrc=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/record_web/assets/js/record.fixwebmduration.js</key>
		<dict>
			<key>hash2</key>
			<data>
			w0WtXS//s1etpke5tpBpNFKiABvFkS8ekGaHbOAWOZs=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/record_web/assets/js/record.worklet.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ah9hDFIXh/HE9PdxbofOIsvMUTZO9R7LxLCujd/pwRk=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/wakelock_plus/assets/no_sleep.js</key>
		<dict>
			<key>hash2</key>
			<data>
			3OTu8LGXtkCtaqqyIo7h7n3M+L1ta13lSE3RvRZDCng=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/youtube_player_flutter/assets/speedometer.webp</key>
		<dict>
			<key>hash2</key>
			<data>
			UyJsGpac7pIldiQobLwRFgmPJCSQLk0Ald1JuO/hCTg=
			</data>
		</dict>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			TGVjYgE+Oyl6guvhhPPrWfynkxkJeFjSzSLsQqn7Q3M=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
