
'.env
asset.env assets/fonts/Geologica-Black.ttf
asset assets/fonts/Geologica-Black.ttfassets/fonts/Geologica-Bold.ttf
assetassets/fonts/Geologica-Bold.ttf$assets/fonts/Geologica-ExtraBold.ttf
asset$assets/fonts/Geologica-ExtraBold.ttf%assets/fonts/Geologica-ExtraLight.ttf
asset%assets/fonts/Geologica-ExtraLight.ttf assets/fonts/Geologica-Light.ttf
asset assets/fonts/Geologica-Light.ttf!assets/fonts/Geologica-Medium.ttf
asset!assets/fonts/Geologica-Medium.ttf"assets/fonts/Geologica-Regular.ttf
asset"assets/fonts/Geologica-Regular.ttf#assets/fonts/Geologica-SemiBold.ttf
asset#assets/fonts/Geologica-SemiBold.ttfassets/fonts/Geologica-Thin.ttf
assetassets/fonts/Geologica-Thin.ttf;assets/fonts/Geologica-VariableFont_CRSV,SHRP,slnt,wght.ttf
asset;assets/fonts/Geologica-VariableFont_CRSV,SHRP,slnt,wght.ttf(assets/fonts/Geologica_Cursive-Black.ttf
asset(assets/fonts/Geologica_Cursive-Black.ttf'assets/fonts/Geologica_Cursive-Bold.ttf
asset'assets/fonts/Geologica_Cursive-Bold.ttf,assets/fonts/Geologica_Cursive-ExtraBold.ttf
asset,assets/fonts/Geologica_Cursive-ExtraBold.ttf-assets/fonts/Geologica_Cursive-ExtraLight.ttf
asset-assets/fonts/Geologica_Cursive-ExtraLight.ttf(assets/fonts/Geologica_Cursive-Light.ttf
asset(assets/fonts/Geologica_Cursive-Light.ttf)assets/fonts/Geologica_Cursive-Medium.ttf
asset)assets/fonts/Geologica_Cursive-Medium.ttf*assets/fonts/Geologica_Cursive-Regular.ttf
asset*assets/fonts/Geologica_Cursive-Regular.ttf+assets/fonts/Geologica_Cursive-SemiBold.ttf
asset+assets/fonts/Geologica_Cursive-SemiBold.ttf'assets/fonts/Geologica_Cursive-Thin.ttf
asset'assets/fonts/Geologica_Cursive-Thin.ttf#assets/fonts/JetBrainsMono-Bold.ttf
asset#assets/fonts/JetBrainsMono-Bold.ttf&assets/fonts/JetBrainsMono-Regular.ttf
asset&assets/fonts/JetBrainsMono-Regular.ttfassets/fonts/NotoSans-Bold.ttf
assetassets/fonts/NotoSans-Bold.ttfassets/fonts/NotoSans-Light.ttf
assetassets/fonts/NotoSans-Light.ttf!assets/fonts/NotoSans-Regular.ttf
asset!assets/fonts/NotoSans-Regular.ttf$assets/fonts/NunitoSans-Variable.ttf
asset$assets/fonts/NunitoSans-Variable.ttfassets/fonts/PTMono-Regular.ttf
assetassets/fonts/PTMono-Regular.ttfassets/fonts/Roboto-Bold.ttf
assetassets/fonts/Roboto-Bold.ttfassets/fonts/Roboto-Medium.ttf
assetassets/fonts/Roboto-Medium.ttfassets/fonts/Roboto-Regular.ttf
assetassets/fonts/Roboto-Regular.ttf2packages/cupertino_icons/assets/CupertinoIcons.ttf
asset2packages/cupertino_icons/assets/CupertinoIcons.ttf;packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css
asset;packages/flutter_inappwebview/assets/t_rex_runner/t-rex.css<packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html
asset<packages/flutter_inappwebview/assets/t_rex_runner/t-rex.html;packages/flutter_inappwebview_web/assets/web/web_support.js
asset;packages/flutter_inappwebview_web/assets/web/web_support.js'packages/lucide_icons/assets/lucide.ttf
asset'packages/lucide_icons/assets/lucide.ttf7packages/record_web/assets/js/record.fixwebmduration.js
asset7packages/record_web/assets/js/record.fixwebmduration.js/packages/record_web/assets/js/record.worklet.js
asset/packages/record_web/assets/js/record.worklet.js)packages/wakelock_plus/assets/no_sleep.js
asset)packages/wakelock_plus/assets/no_sleep.js7packages/youtube_player_flutter/assets/speedometer.webp
asset7packages/youtube_player_flutter/assets/speedometer.webp