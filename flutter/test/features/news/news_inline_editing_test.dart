import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/features/news/widgets/news_inline_editor.dart';

void main() {
  group('News Inline Editing Tests', () {
    testWidgets('NewsInlineEditor should display correctly for new news', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewsInlineEditor(
                newsId: null,
                initialContent: '',
              ),
            ),
          ),
        ),
      );

      // Проверяем, что заголовок для создания новости отображается
      expect(find.text('Создание новости'), findsOneWidget);
      
      // Проверяем, что кнопка "Опубликовать" отображается
      expect(find.text('Опубликовать'), findsOneWidget);
      
      // Проверяем, что кнопка "Отмена" отображается
      expect(find.text('Отмена'), findsOneWidget);
      
      // Проверяем, что поле ввода отображается
      expect(find.byType(TextField), findsOneWidget);
    });

    testWidgets('NewsInlineEditor should display correctly for editing existing news', (WidgetTester tester) async {
      const testContent = 'Test news content';
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewsInlineEditor(
                newsId: 1,
                initialContent: testContent,
              ),
            ),
          ),
        ),
      );

      // Проверяем, что заголовок для редактирования отображается
      expect(find.text('Редактирование новости'), findsOneWidget);
      
      // Проверяем, что кнопка "Сохранить" отображается
      expect(find.text('Сохранить'), findsOneWidget);
      
      // Проверяем, что начальное содержимое загружено
      expect(find.text(testContent), findsOneWidget);
    });

    test('NewsEditingState should handle state transitions correctly', () {
      // Тест начального состояния
      const initialState = NewsEditingState();
      expect(initialState.hasActiveEditing, false);
      expect(initialState.isCreatingNew, false);
      expect(initialState.editingNewsId, null);
      expect(initialState.editingContent, '');

      // Тест состояния создания новой новости
      final creatingState = initialState.copyWith(
        isCreatingNew: true,
        editingContent: 'New content',
      );
      expect(creatingState.hasActiveEditing, true);
      expect(creatingState.isCreatingNew, true);
      expect(creatingState.editingContent, 'New content');

      // Тест состояния редактирования существующей новости
      final editingState = initialState.copyWith(
        editingNewsId: 1,
        editingContent: 'Edited content',
        isCreatingNew: false,
      );
      expect(editingState.hasActiveEditing, true);
      expect(editingState.isCreatingNew, false);
      expect(editingState.isEditingNews(1), true);
      expect(editingState.isEditingNews(2), false);

      // Тест сброса состояния
      final resetState = editingState.reset();
      expect(resetState.hasActiveEditing, false);
      expect(resetState.isCreatingNew, false);
      expect(resetState.editingNewsId, null);
      expect(resetState.editingContent, '');
    });

    test('NewsEditingState.isEditingNews should work correctly', () {
      const state = NewsEditingState();
      
      // Тест когда новость не редактируется
      expect(state.isEditingNews(1), false);
      
      // Тест когда создается новая новость
      final creatingState = state.copyWith(isCreatingNew: true);
      expect(creatingState.isEditingNews(1), false);
      
      // Тест когда редактируется конкретная новость
      final editingState = state.copyWith(
        editingNewsId: 1,
        isCreatingNew: false,
      );
      expect(editingState.isEditingNews(1), true);
      expect(editingState.isEditingNews(2), false);
    });
  });
}
