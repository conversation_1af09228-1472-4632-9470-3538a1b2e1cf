import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/navigation/app_router.dart';
import 'package:luxury_app/theme/theme.dart';
import 'package:luxury_app/theme/theme_provider.dart';

/// Основной класс приложения
/// СЛОЙ ПРЕДСТАВЛЕНИЯ (PRESENTATION LAYER)
/// Точка входа в приложения, отвечает за инициализацию глобальных зависимостей
class App extends StatelessWidget {
  const App({super.key});

  @override
  Widget build(BuildContext context) {
    return const _AppWithProviders();
  }
}

/// Основное приложение с провайдерами блоков
class _AppWithProviders extends ConsumerWidget {
  const _AppWithProviders();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode = ref.watch(themeProvider);
    final router = ref.watch(goRouterProvider);

    // Инициализируем BackgroundSyncService только для авторизованных пользователей
    ref.watch(backgroundSyncInitProvider);

    return MaterialApp.router(
      title: 'Luxury App',
      theme: getLightTheme(context),
      darkTheme: getDarkTheme(context),
      themeMode: themeMode,
      routerConfig: router,
      debugShowCheckedModeBanner: false,
      builder: (context, child) {
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          return Container(
            color: Colors.red[100],
            padding: const EdgeInsets.all(16),
            child: Center(
              child: Column(
                children: [
                  const Icon(Icons.error, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(
                    'Ошибка: ${errorDetails.exception}',
                    style: const TextStyle(color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        };
        return child!;
      },
    );
  }
}
