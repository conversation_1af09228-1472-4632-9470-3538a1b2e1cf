import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:luxury_app/core/services/background_sync_service.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/core/services/supabase_service.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_repository.dart';
import 'package:luxury_app/features/news/news_repository.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/auth_cache_service.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_repository.dart';
import 'package:luxury_app/shared/constants/supabase_constants.dart';

/// Инициализатор приложения
/// Отвечает за инициализацию всех сервисов и зависимостей
class AppInitializer {
  static bool _isInitialized = false;

  /// Инициализация всех сервисов приложения
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Инициализация переменных окружения
      await dotenv.load(fileName: '.env');

      // Инициализация Hive (первым делом - он не зависит от нативных каналов)
      await Hive.initFlutter();

      // Инициализируем кэш сервисы (они используют Hive, а не SQLite)
      await CacheService.instance.init();
      await AuthCacheService.instance.init();

      // Инициализируем сервис подключения
      await ConnectivityService.instance.init();

      // Пытаемся инициализировать Supabase, но не падаем при ошибке
      try {
        await SupabaseService.initialize(
          url: SupabaseConstants.url,
          anonKey: SupabaseConstants.anonKey,
        );

        if (kDebugMode) {
          print('✅ Supabase успешно инициализирован');
          print('🌍 Окружение: ${SupabaseConstants.environment}');
          print('🔗 URL: ${SupabaseConstants.url}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Не удалось подключиться к Supabase: $e');
          print('🌍 Окружение: ${SupabaseConstants.environment}');
          print('📱 Приложение будет работать в offline режиме');
        }
        // Не прерываем инициализацию - приложение должно работать offline
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ AppInitializer: Все сервисы успешно инициализированы');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ AppInitializer: Ошибка инициализации - $e');
      }
      rethrow;
    }
  }

  /// Инициализация фоновой синхронизации
  static Future<void> initBackgroundSync({
    required WikiRepository wikiRepository,
    required AIChatRepository chatRepository,
    required NewsRepository newsRepository,
  }) async {
    try {
      await BackgroundSyncService.instance.init(
        wikiRepository: wikiRepository,
        chatRepository: chatRepository,
        newsRepository: newsRepository,
      );

      if (kDebugMode) {
        print('✅ BackgroundSyncService инициализирован');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Ошибка инициализации BackgroundSyncService: $e');
      }
      // Не прерываем работу приложения из-за ошибки фоновой синхронизации
    }

    // Инициализируем PermissionService для авторизованных пользователей
    try {
      await PermissionService.instance.initialize();

      if (kDebugMode) {
        print('✅ PermissionService инициализирован');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Ошибка инициализации PermissionService: $e');
      }
      // Не прерываем работу приложения из-за ошибки прав доступа
    }
  }

  /// Проверка инициализации
  static bool get isInitialized => _isInitialized;
}
