/// Конфигурация маршрутов приложения
class RouteConfig {
  RouteConfig._();

  // Константы для путей
  static const String newsPath = '/news';
  static const String wikiPath = '/wiki/:pageId';
  static const String aiPath = '/ai/:chatId';
  static const String usersPath = '/users';
  static const String userPermissionsPath = '/users/:userId/permissions';

  static const String qrScannerPath = '/qr-scanner';
  static const String settingsPath = '/settings';

  // Имена маршрутов для удобства ссылок
  static const String newsRoute = 'news';
  static const String wikiRoute = 'wiki';
  static const String aiRoute = 'ai';
  static const String usersRoute = 'users';
  static const String userPermissionsRoute = 'user-permissions';

  static const String qrScannerRoute = 'qr-scanner';
  static const String settingsRoute = 'settings';
}
