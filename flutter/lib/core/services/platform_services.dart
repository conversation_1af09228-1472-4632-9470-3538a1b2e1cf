import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:luxury_app/core/mixins/logger_mixin.dart';
// Mermaid Service Imports
import 'package:luxury_app/shared/widgets/base_mermaid_service.dart';
import 'package:luxury_app/shared/widgets/mermaid_service_io.dart';

/// Управляет платформо-специфичными сервисами, обеспечивая единый экземпляр (Singleton).
class PlatformServices with LoggerMixin {
  PlatformServices._(); // Private constructor

  // --- Mermaid Service ---
  static BaseMermaidService? _mermaidServiceInstance;

  /// Возвращает единый экземпляр сервиса для рендеринга Mermaid диаграмм.
  static BaseMermaidService get mermaidService {
    if (kIsWeb) {
      // Для веб-версии возвращаем простую заглушку
      _mermaidServiceInstance ??= _WebMermaidServiceStub();
      return _mermaidServiceInstance!;
    } else {
      // Для нативных платформ используем единый экземпляр NativeMermaidService
      _mermaidServiceInstance ??= NativeMermaidService();
      return _mermaidServiceInstance!;
    }
  }

  /// Освобождает ресурсы, используемые сервисами.
  static Future<void> dispose() async {
    // Освобождаем ресурсы Mermaid сервиса
    if (_mermaidServiceInstance != null) {
      await _mermaidServiceInstance!.dispose();
      _mermaidServiceInstance = null;
    }
  }
}


/// Простая заглушка для веб-версии
class _WebMermaidServiceStub implements BaseMermaidService {
  @override
  Future<String> render(String cleanCode) async {
    return '<div>Mermaid диаграммы не поддерживаются в веб-версии</div>';
  }

  @override
  Future<void> dispose() async {
    // Нет ресурсов для очистки
  }
}
