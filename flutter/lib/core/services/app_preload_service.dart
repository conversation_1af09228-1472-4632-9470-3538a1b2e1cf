import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:luxury_app/features/news/news_repository.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/core/services/permission_service.dart';

/// Сервис предзагрузки критически важных данных приложения
/// Загружает данные параллельно с процессом авторизации
class AppPreloadService {
  static final AppPreloadService _instance = AppPreloadService._internal();
  static AppPreloadService get instance => _instance;
  AppPreloadService._internal();

  // Репозитории для загрузки данных
  NewsRepository? _newsRepository;
  PermissionService? _permissionService;

  // Кэш предзагруженных данных
  List<NewsItem>? _preloadedNews;
  bool _newsPreloadCompleted = false;
  bool _permissionsPreloadCompleted = false;

  // Комплитеры для отслеживания завершения загрузки
  Completer<List<NewsItem>>? _newsCompleter;
  Completer<void>? _permissionsCompleter;

  /// Инициализация сервиса с необходимыми зависимостями
  void initialize({
    required NewsRepository newsRepository,
    required PermissionService permissionService,
  }) {
    _newsRepository = newsRepository;
    _permissionService = permissionService;
    
    if (kDebugMode) {
      log('✅ AppPreloadService инициализирован');
    }
  }

  /// Запуск предзагрузки данных для авторизованного пользователя
  Future<void> startPreloading() async {
    if (_newsRepository == null || _permissionService == null) {
      if (kDebugMode) {
        log('⚠️ AppPreloadService не инициализирован, пропускаем предзагрузку');
      }
      return;
    }

    if (kDebugMode) {
      log('🚀 Запуск предзагрузки данных...');
    }

    // Сбрасываем состояние
    _resetState();

    // Запускаем предзагрузку параллельно
    final futures = <Future<void>>[
      _preloadNews(),
      _preloadPermissions(),
    ];

    // Ждем завершения всех задач, но не блокируем выполнение при ошибках
    await Future.wait(futures, eagerError: false);

    if (kDebugMode) {
      log('✅ Предзагрузка завершена');
    }
  }

  /// Предзагрузка новостей
  Future<void> _preloadNews() async {
    try {
      _newsCompleter = Completer<List<NewsItem>>();
      
      if (kDebugMode) {
        log('📰 Предзагрузка новостей...');
      }

      final news = await _newsRepository!.getNews(forceRefresh: false);
      _preloadedNews = news;
      _newsPreloadCompleted = true;
      
      _newsCompleter!.complete(news);

      if (kDebugMode) {
        log('✅ Новости предзагружены: ${news.length} записей');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка предзагрузки новостей: $e');
      }
      _newsCompleter?.completeError(e);
    }
  }

  /// Предзагрузка прав пользователя
  Future<void> _preloadPermissions() async {
    try {
      _permissionsCompleter = Completer<void>();
      
      if (kDebugMode) {
        log('🔐 Предзагрузка прав пользователя...');
      }

      await _permissionService!.initialize();
      _permissionsPreloadCompleted = true;
      
      _permissionsCompleter!.complete();

      if (kDebugMode) {
        log('✅ Права пользователя предзагружены');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка предзагрузки прав: $e');
      }
      _permissionsCompleter?.completeError(e);
    }
  }

  /// Получение предзагруженных новостей
  /// Возвращает данные немедленно если они уже загружены,
  /// или ждет завершения загрузки
  Future<List<NewsItem>?> getPreloadedNews() async {
    if (_newsPreloadCompleted && _preloadedNews != null) {
      if (kDebugMode) {
        log('📰 Возвращаем предзагруженные новости: ${_preloadedNews!.length} записей');
      }
      return _preloadedNews;
    }

    if (_newsCompleter != null && !_newsCompleter!.isCompleted) {
      try {
        if (kDebugMode) {
          log('⏳ Ждем завершения предзагрузки новостей...');
        }
        return await _newsCompleter!.future;
      } catch (e) {
        if (kDebugMode) {
          log('❌ Ошибка получения предзагруженных новостей: $e');
        }
        return null;
      }
    }

    return null;
  }

  /// Проверка готовности предзагруженных данных
  bool get isNewsPreloaded => _newsPreloadCompleted;
  bool get isPermissionsPreloaded => _permissionsPreloadCompleted;
  bool get isFullyPreloaded => _newsPreloadCompleted && _permissionsPreloadCompleted;

  /// Сброс состояния сервиса
  void _resetState() {
    _preloadedNews = null;
    _newsPreloadCompleted = false;
    _permissionsPreloadCompleted = false;
    _newsCompleter = null;
    _permissionsCompleter = null;
  }

  /// Очистка кэша предзагруженных данных
  void clearCache() {
    if (kDebugMode) {
      log('🧹 Очистка кэша предзагруженных данных');
    }
    _resetState();
  }
}
