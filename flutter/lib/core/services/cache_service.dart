import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:path_provider/path_provider.dart';

/// Сервис для кеширования данных с использованием Hive
class CacheService with LoggerMixin {
  // Константы для ключей кэширования
  static const String _chatsListKey = 'chats_list';
  static const String _chatMessagesPrefix = 'chat_messages_';
  static const String _newsListKey = 'news_list';
  static const String _newsHashKey = 'news_hash';
  static const String _themeKey = 'theme_mode';
  static const String _wikiHierarchyKey = 'wiki_hierarchy';
  static const String _wikiFileContentPrefix = 'wiki_content_';
  static const String _wikiScrollPrefix = 'wiki_scroll_';
  static const String _audioPositionPrefix = 'audio_position_';

  /// Сохраняет позицию воспроизведения аудиофайла (в секундах)
  Future<void> saveAudioPosition(String fileId, int seconds) async {
    await _saveWithPrefix(_audioPositionPrefix, fileId, seconds);
  }

  /// Получает позицию воспроизведения аудиофайла (в секундах)
  int? getAudioPosition(String fileId) {
    return _getWithPrefix<int>(_audioPositionPrefix, fileId);
  }

  /// Удаляет позицию воспроизведения аудиофайла
  Future<void> removeAudioPosition(String fileId) async {
    await _removeWithPrefix(_audioPositionPrefix, fileId);
  }

  /// Экземпляр Hive box
  Box? _box; // Используем box 'main'
  Completer<void>? _initCompleter;

  /// Stream для уведомлений об очистке кэша
  final StreamController<bool> _cacheClearedController =
      StreamController<bool>.broadcast();

  /// Поток уведомлений об очистке кэша
  Stream<bool> get cacheClearedStream => _cacheClearedController.stream;

  /// Приватный конструктор для синглтона
  CacheService._();

  /// Публичный геттер для box (только для чтения)
  Box? get box => _box;

  /// Глобальный экземпляр сервиса
  static final CacheService instance = CacheService._();

  /// Инициализирует сервис кеширования
  Future<void> init() async {
    if (_box != null) return;
    if (_initCompleter != null) {
      return _initCompleter!.future;
    }
    _initCompleter = Completer<void>();
    try {
      await Hive.initFlutter();
      _box = await Hive.openBox('main');
      _initCompleter!.complete();
    } catch (e) {
      _initCompleter!.completeError(e);
      _initCompleter = null;
      rethrow;
    }
  }

  /// Сохраняет список чатов в кэш
  Future<bool> saveChats(String chatsJson) async {
    return await _safePut(_chatsListKey, chatsJson);
  }

  /// Получает список чатов из кэша
  String? getChats() {
    return _safeGet<String>(_chatsListKey);
  }

  /// Удаляет кэш списка чатов (инвалидация)
  Future<void> removeChatsList() async {
    await init(); // Убедимся, что box инициализирован
    await _box?.delete(_chatsListKey);
  }

  /// Сохраняет сообщения чата в кэш
  Future<bool> saveChatMessages(int chatId, String messagesJson) async {
    await _saveWithPrefix(_chatMessagesPrefix, chatId.toString(), messagesJson);
    return true;
  }

  /// Получает сообщения чата из кэша
  String? getChatMessages(int chatId) {
    return _getWithPrefix<String>(_chatMessagesPrefix, chatId.toString());
  }

  /// Удаляет сообщения указанного чата из кэша
  Future<bool> removeChatMessages(int chatId) async {
    await _removeWithPrefix(_chatMessagesPrefix, chatId.toString());
    return true;
  }

  /// Очищает все данные чатов
  Future<void> clearAllChatsData() async {
    await init();
    // Удаляем список чатов
    await _box!.delete(_chatsListKey);
    // Удаляем все сообщения чатов
    await _clearByPrefix(_chatMessagesPrefix);
  }

  // --- Методы для кэширования новостей ---
  Future<bool> saveNews(String newsJson) async {
    return await _safePut(_newsListKey, newsJson);
  }

  String? getNews() {
    return _safeGet<String>(_newsListKey);
  }

  Future<bool> clearNews() async {
    await _box!.delete(_newsListKey);
    return true;
  }

  // --- Методы для хранения хэша новостей ---
  Future<void> saveNewsHash(String hash) async {
    await _safePut(_newsHashKey, hash);
  }

  String? getNewsHash() {
    return _safeGet<String>(_newsHashKey);
  }

  // --- Методы для кэширования темы ---
  Future<void> saveThemeMode(String mode) async {
    await _safePut(_themeKey, mode);
  }

  String? getThemeMode() {
    return _safeGet<String>(_themeKey);
  }

  // --- Методы для настроек приложения ---
  static const String _drawerWidthKey = 'drawer_width';
  static const double _defaultDrawerWidth = 280.0;

  /// Сохраняет ширину drawer
  Future<void> saveDrawerWidth(double width) async {
    await _safePut(_drawerWidthKey, width);
  }

  /// Получает ширину drawer
  double getDrawerWidth() {
    return _safeGet<double>(_drawerWidthKey) ?? _defaultDrawerWidth;
  }

  /// Сбрасывает ширину drawer к значению по умолчанию
  Future<void> resetDrawerWidth() async {
    await removeData(_drawerWidthKey);
  }

  // --- Методы для кэширования Wiki ---

  /// Сохраняет иерархию Wiki в кэш (в виде JSON строки)
  Future<bool> saveWikiHierarchy(String hierarchyJson) async {
    return await _safePut(_wikiHierarchyKey, hierarchyJson);
  }

  /// Получает иерархию Wiki из кэша (в виде JSON строки)
  String? getWikiHierarchy() {
    return _safeGet<String>(_wikiHierarchyKey);
  }

  /// Сохраняет содержимое Markdown файла Wiki в кэш
  Future<bool> saveWikiFileContent(String filePath, String content) async {
    await _saveWithPrefix(_wikiFileContentPrefix, filePath, content);
    return true;
  }

  /// Получает содержимое Markdown файла Wiki из кэша
  String? getWikiFileContent(String filePath) {
    return _getWithPrefix<String>(_wikiFileContentPrefix, filePath);
  }

  /// Удаляет содержимое файла Wiki из кэша
  Future<bool> removeWikiFileContent(String filePath) async {
    await _removeWithPrefix(_wikiFileContentPrefix, filePath);
    return true;
  }

  /// Сохраняет позицию скролла для wiki-файла
  Future<void> saveWikiScrollPosition(String fileId, double offset) async {
    await _saveWithPrefix(_wikiScrollPrefix, fileId, offset);
  }

  /// Получает позицию скролла для wiki-файла
  double? getWikiScrollPosition(String fileId) {
    return _getWithPrefix<double>(_wikiScrollPrefix, fileId);
  }

  /// Удаляет позицию скролла для wiki-файла
  Future<bool> removeWikiScrollPosition(String fileId) async {
    await _removeWithPrefix(_wikiScrollPrefix, fileId);
    return true;
  }

  /// Сохраняет произвольные данные в кеш
  Future<bool> saveData(String key, dynamic value) async {
    try {
      await init();
      await _box!.put(key, value);
      return true;
    } catch (e) {
      logError('Ошибка при сохранении данных в кэш: $key', e);
      return false;
    }
  }

  /// Получает данные из кеша по ключу
  dynamic getData(String key, {Type? expectedType}) {
    try {
      final value = _box?.get(key);

      if (value == null) return null;

      if (expectedType != null && value is String) {
        try {
          final decoded = jsonDecode(value);
          if ((expectedType == Map && decoded is Map) ||
              (expectedType == List && decoded is List)) {
            return decoded;
          }
          if (expectedType == String) {
            return value;
          }
        } catch (e) {
          // Если не удалось декодировать JSON, но ожидается строка
          if (expectedType == String) {
            return value;
          }
          logWarning('Ошибка декодирования JSON для ключа $key: $e');
          return null;
        }
      }

      if (expectedType == null || value.runtimeType == expectedType) {
        return value;
      }

      logWarning(
        'Тип данных не совпадает для ключа $key: ожидается $expectedType, получен ${value.runtimeType}',
      );
      return null;
    } catch (e) {
      logError('Ошибка при получении данных из кэша: $key', e);
      return null;
    }
  }

  /// Удаляет данные из кеша по ключу
  Future<bool> removeData(String key) async {
    try {
      await init();
      if (_box != null) {
        await _box!.delete(key);
      }
      return true;
    } catch (e) {
      logError('Ошибка при удалении данных из кэша: $key', e);
      return false;
    }
  }

  // --- Вспомогательные методы для унификации работы с префиксами ---

  /// Сохраняет данные с префиксом
  Future<void> _saveWithPrefix(String prefix, String id, dynamic value) async {
    await init();
    final key = '$prefix$id';
    await _box!.put(key, value);
  }

  /// Получает данные с префиксом
  T? _getWithPrefix<T>(String prefix, String id) {
    final key = '$prefix$id';
    final value = _box?.get(key);
    if (value is T) return value;
    // Попытка конвертации для числовых типов
    if (T == double && value is int) return value.toDouble() as T;
    if (T == int && value is double) return value.toInt() as T;
    return null;
  }

  /// Удаляет данные с префиксом
  Future<void> _removeWithPrefix(String prefix, String id) async {
    try {
      await init();
      final key = '$prefix$id';
      await _box?.delete(key);
    } catch (e) {
      logError('Ошибка при удалении данных с префиксом $prefix: $id', e);
    }
  }

  /// Безопасно получает данные с автоматической инициализацией
  T? _safeGet<T>(String key) {
    try {
      if (_box == null) {
        // Не запускаем асинхронную инициализацию в синхронном методе
        // Возвращаем null, чтобы избежать race condition
        return null;
      }
      final value = _box!.get(key);
      return value is T ? value : null;
    } catch (e) {
      logError('Ошибка при безопасном получении данных: $key', e);
      return null;
    }
  }

  /// Безопасно сохраняет данные с проверкой инициализации
  Future<bool> _safePut(String key, dynamic value) async {
    try {
      await init();
      if (_box != null) {
        await _box!.put(key, value);
        return true;
      }
      return false;
    } catch (e) {
      logError('Ошибка при сохранении в кэш: $key', e);
      return false;
    }
  }

  /// Очищает все данные с определенным префиксом
  Future<void> _clearByPrefix(String prefix) async {
    try {
      await init();
      if (_box == null) return;

      final keys =
          _box!.keys
              .where((key) => key is String && key.startsWith(prefix))
              .toList();

      for (final key in keys) {
        await _box!.delete(key);
      }
    } catch (e) {
      logError('Ошибка при очистке данных с префиксом: $prefix', e);
    }
  }

  // --- Методы для кэширования аудиофайлов ---

  static const String _audioCacheDir = 'audio_cache';

  /// Возвращает путь к директории для кэша аудиофайлов
  Future<Directory> _getAudioCacheDir() async {
    if (kIsWeb) throw UnsupportedError('Audio cache not supported on web');
    final dir = await getApplicationDocumentsDirectory();
    final audioDir = Directory('${dir.path}/$_audioCacheDir');
    if (!await audioDir.exists()) {
      await audioDir.create(recursive: true);
    }
    return audioDir;
  }

  /// Проверяет, есть ли аудиофайл в кэше
  Future<bool> hasAudio(String fileId) async {
    if (kIsWeb) return false;
    final file = await getAudioFile(fileId);
    return file?.existsSync() ?? false;
  }

  /// Возвращает File для аудиофайла по fileId
  Future<File?> getAudioFile(String fileId) async {
    if (kIsWeb) return null;
    final dir = await _getAudioCacheDir();
    return File('${dir.path}/$fileId.mp3');
  }

  /// Сохраняет аудиофайл в кэш
  Future<File?> saveAudio(String fileId, List<int> bytes) async {
    if (kIsWeb) return null;
    final file = await getAudioFile(fileId);
    if (file != null) {
      await file.writeAsBytes(bytes, flush: true);
    }
    return file;
  }

  /// Удаляет аудиофайл из кэша
  Future<void> removeAudio(String fileId) async {
    if (kIsWeb) return;
    final file = await getAudioFile(fileId);
    if (file != null && await file.exists()) {
      await file.delete();
    }
  }

  /// Очищает весь кэш аудио
  Future<void> clearAudioCache() async {
    if (kIsWeb) return;
    final dir = await _getAudioCacheDir();
    if (await dir.exists()) {
      await dir.delete(recursive: true);
      await dir.create(recursive: true);
    }
  }

  // --- Методы для кэширования Mermaid диаграмм ---

  static const String _mermaidPrefix = 'mermaid_';

  /// Сохраняет SVG диаграммы Mermaid
  Future<void> saveMermaidSvg(String hash, String svg) async {
    await _saveWithPrefix(_mermaidPrefix, hash, svg);
  }

  /// Получает SVG диаграммы Mermaid
  String? getMermaidSvg(String hash) {
    return _getWithPrefix<String>(_mermaidPrefix, hash);
  }

  /// Удаляет SVG диаграммы Mermaid
  Future<void> removeMermaidSvg(String hash) async {
    await _removeWithPrefix(_mermaidPrefix, hash);
  }

  /// Очищает весь кэш Mermaid диаграмм
  Future<void> clearMermaidCache() async {
    await _clearByPrefix(_mermaidPrefix);
  }

  // --- Методы для кэширования изображений ---

  static const String _imageSizePrefix = 'image_size_';
  static const String _imageFilePrefix = 'image_file_';

  /// Сохраняет размер изображения
  Future<void> saveImageSize(String url, int width, int height) async {
    await _saveWithPrefix(_imageSizePrefix, url, {'w': width, 'h': height});
  }

  /// Получает размер изображения
  Map<String, int>? getImageSize(String url) {
    final data = _getWithPrefix<Map>(_imageSizePrefix, url);
    if (data != null && data['w'] is int && data['h'] is int) {
      return {'w': data['w'], 'h': data['h']};
    }
    return null;
  }

  /// Получает кэшированный файл изображения
  Future<File?> getCachedImageFile(String url) async {
    if (kIsWeb) return null;
    final file = await _getImageFile(url);
    return file.existsSync() ? file : null;
  }

  /// Сохраняет файл изображения
  Future<File?> saveImageFile(String url, Uint8List bytes) async {
    if (kIsWeb) return null;
    final file = await _getImageFile(url);
    final dir = file.parent;
    if (!await dir.exists()) {
      await dir.create(recursive: true);
    }
    await file.writeAsBytes(bytes, flush: true);
    return file;
  }

  /// Получает файл изображения по URL
  Future<File> _getImageFile(String url) async {
    final dir = await getApplicationDocumentsDirectory();
    // Хешируем URL для получения короткого имени файла
    final hash = url.hashCode.toString();
    final path = '${dir.path}/$_imageFilePrefix$hash.png';
    return File(path);
  }

  /// Очищает кэш изображений
  Future<void> clearImageCache() async {
    await _clearByPrefix(_imageSizePrefix);
    if (!kIsWeb) {
      final dir = await getApplicationDocumentsDirectory();
      final files = Directory(
        dir.path,
      ).listSync().where((f) => f.path.contains(_imageFilePrefix));
      for (final f in files) {
        try {
          if (f is File) await f.delete();
        } catch (_) {}
      }
    }
  }

  /// Полная очистка кэша с уведомлением провайдеров
  Future<void> clearAllCache() async {
    try {
      await init();

      // Очищаем Hive box
      await _box?.clear();

      // Очищаем кэш аудио
      await clearAudioCache();

      // Очищаем кэш изображений
      await clearImageCache();

      // Очищаем кэш Mermaid диаграмм
      await clearMermaidCache();

      logInfo('✅ Кэш полностью очищен');

      // Уведомляем всех подписчиков об очистке кэша
      _cacheClearedController.add(true);
    } catch (e) {
      logError('❌ Ошибка при очистке кэша: $e');
      rethrow;
    }
  }

  /// Освобождение ресурсов
  void dispose() {
    _cacheClearedController.close();
  }
}
