import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:luxury_app/core/services/supabase_service.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';

/// Доступные права в системе
class Permissions {
  static const String canCreateNews = 'can_create_news';
  static const String canEditNews = 'can_edit_news';
  static const String canDeleteNews = 'can_delete_news';
  static const String canCreateWiki = 'can_create_wiki';
  static const String canEditWiki = 'can_edit_wiki';
  static const String canDeleteWiki = 'can_delete_wiki';
  static const String canApproveWikiChanges = 'can_approve_wiki_changes';
  static const String canManageAudio = 'can_manage_audio';

  /// Список всех доступных прав
  static const List<String> all = [
    canCreateNews,
    canEditNews,
    canDeleteNews,
    canCreateWiki,
    canEditWiki,
    canDeleteWiki,
    canApproveWikiChanges,
    canManageAudio,
  ];

  /// Человекочитаемые названия прав
  static const Map<String, String> displayNames = {
    canCreateNews: 'Создание новостей',
    canEditNews: 'Редактирование новостей',
    canDeleteNews: 'Удаление новостей',
    canCreateWiki: 'Создание Wiki',
    canEditWiki: 'Редактирование Wiki',
    canDeleteWiki: 'Удаление Wiki',
    canApproveWikiChanges: 'Одобрение изменений Wiki',
    canManageAudio: 'Управление аудиофайлами',
  };
}

/// Модель права пользователя
class UserPermission {
  final String id;
  final String userId;
  final String permissionName;
  final String grantedByUserId;
  final DateTime createdAt;

  const UserPermission({
    required this.id,
    required this.userId,
    required this.permissionName,
    required this.grantedByUserId,
    required this.createdAt,
  });

  factory UserPermission.fromJson(Map<String, dynamic> json) {
    return UserPermission(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      permissionName: json['permission_name'] as String,
      grantedByUserId: json['granted_by_user_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'permission_name': permissionName,
      'granted_by_user_id': grantedByUserId,
      'created_at': createdAt.toIso8601String(),
    };
  }
}

/// Сервис для работы с правами пользователей
class PermissionService {
  static PermissionService? _instance;
  static PermissionService get instance => _instance ??= PermissionService._();

  PermissionService._();

  final SupabaseService _supabaseService = SupabaseService.instance;

  /// Кэш прав текущего пользователя
  List<String>? _cachedUserPermissions;

  /// Проверка, является ли текущий пользователь администратором
  bool get isCurrentUserAdmin {
    final user = _supabaseService.currentUser;
    if (user == null) {
      if (kDebugMode) {
        log('❌ isCurrentUserAdmin: пользователь не авторизован');
      }
      return false;
    }

    final role = user.userMetadata?['role'] as String?;
    final isAdmin = role == 'admin';

    if (kDebugMode) {
      log(
        '🔍 isCurrentUserAdmin: user.id=${user.id}, email=${user.email}, role=$role, isAdmin=$isAdmin',
      );
      log('🔍 userMetadata: ${user.userMetadata}');
    }

    return isAdmin;
  }

  /// Проверка конкретного права у текущего пользователя
  bool hasPermission(String permission) {
    // Администраторы имеют все права
    if (isCurrentUserAdmin) return true;

    // Проверяем в кэше
    if (_cachedUserPermissions != null) {
      return _cachedUserPermissions!.contains(permission);
    }

    // Если кэш пуст, возвращаем false (права будут загружены асинхронно)
    return false;
  }

  /// Получение всех прав текущего пользователя
  Future<List<String>> getCurrentUserPermissions() async {
    try {
      final userId = _supabaseService.currentUserId;
      if (userId == null) return [];

      // Если администратор, возвращаем все права
      if (isCurrentUserAdmin) {
        _cachedUserPermissions = List.from(Permissions.all);
        return _cachedUserPermissions!;
      }

      final response = await _supabaseService.client
          .from('user_permissions')
          .select('permission_name')
          .eq('user_id', userId);

      final permissions =
          (response as List)
              .map((item) => item['permission_name'] as String)
              .toList();

      _cachedUserPermissions = permissions;

      if (kDebugMode) {
        log('✅ Загружены права пользователя: $permissions');
      }

      return permissions;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки прав пользователя: $e');
      }
      return [];
    }
  }

  /// Получение всех пользователей (только для админов)
  Future<List<User>> getAllUsers() async {
    if (kDebugMode) {
      log('🚀 getAllUsers: проверяем права администратора...');
    }

    if (!isCurrentUserAdmin) {
      if (kDebugMode) {
        log('❌ getAllUsers: недостаточно прав для просмотра пользователей');
      }
      throw Exception('Недостаточно прав для просмотра пользователей');
    }

    try {
      if (kDebugMode) {
        log('🌐 getAllUsers: загружаем пользователей из Supabase...');

        // Добавляем отладочную информацию о текущем пользователе
        try {
          final debugInfo = await _supabaseService.client.rpc(
            'debug_user_info',
          );
          log('🔍 Отладочная информация о пользователе: $debugInfo');
        } catch (debugError) {
          log('⚠️ Не удалось получить отладочную информацию: $debugError');
        }
      }

      // Используем новую упрощенную RPC функцию
      try {
        final response = await _supabaseService.client.rpc('get_users_simple');

        if (response == null) {
          if (kDebugMode) {
            log('⚠️ Получен null ответ от get_users_simple');
          }
          return [];
        }

        // Функция возвращает List с переименованными полями
        final usersList = response as List;
        final users =
            usersList.map((json) {
              final data = json as Map<String, dynamic>;
              // Преобразуем имена полей обратно к ожидаемым
              return User.fromJson({
                'id': data['user_id'],
                'email': data['user_email'],
                'phone': data['user_phone'],
                'role': data['user_role'],
                'created_at': data['user_created_at'],
                'metadata': data['user_metadata'],
              });
            }).toList();

        if (kDebugMode) {
          log(
            '✅ Загружено ${users.length} пользователей через get_users_simple',
          );
        }

        return users;
      } catch (rpcError) {
        if (kDebugMode) {
          log('⚠️ Новая RPC функция не сработала: $rpcError');
          log('🔄 Пробуем старую RPC функцию...');
        }

        // Fallback к старой RPC функции
        try {
          final response = await _supabaseService.client.rpc('get_all_users');
          final users =
              (response as List)
                  .map((json) => User.fromJson(json as Map<String, dynamic>))
                  .toList();

          if (kDebugMode) {
            log('✅ Загружено ${users.length} пользователей через старую RPC');
          }

          return users;
        } catch (oldRpcError) {
          if (kDebugMode) {
            log('⚠️ Старая RPC функция тоже не сработала: $oldRpcError');
            log('🔄 Пробуем альтернативный метод...');
          }

          // Альтернативный метод
          return await _getUsersAlternativeMethod();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки пользователей: $e');
        log('❌ Тип ошибки: ${e.runtimeType}');
        if (e.toString().contains('permission denied')) {
          log(
            '❌ Ошибка прав доступа - возможно пользователь не является администратором',
          );
        }
      }
      rethrow;
    }
  }

  /// Альтернативный метод получения пользователей
  Future<List<User>> _getUsersAlternativeMethod() async {
    try {
      // Получаем уникальных пользователей из таблицы user_permissions
      final permissionsResponse = await _supabaseService.client
          .from('user_permissions')
          .select('user_id')
          .order('created_at', ascending: false);

      // Извлекаем уникальные user_id
      final userIds = <String>{};
      for (final item in permissionsResponse as List) {
        userIds.add(item['user_id'] as String);
      }

      if (kDebugMode) {
        log(
          '🔍 Найдено ${userIds.length} уникальных пользователей в user_permissions',
        );
      }

      // Создаем пользователей с базовой информацией
      final users = <User>[];

      // Добавляем текущего пользователя (администратора)
      final currentUser = _supabaseService.currentUser;
      if (currentUser != null) {
        final currentUserRole =
            currentUser.userMetadata?['role'] as String? ?? 'user';
        users.add(
          User(
            id: currentUser.id,
            email: currentUser.email ?? 'Неизвестно',
            phone: currentUser.phone,
            role: currentUserRole,
            createdAt: DateTime.parse(currentUser.createdAt),
            metadata: currentUser.userMetadata ?? {},
          ),
        );

        if (kDebugMode) {
          log('✅ Добавлен текущий пользователь: ${currentUser.email}');
        }
      }

      // Для других пользователей создаем записи с ограниченной информацией
      for (final userId in userIds) {
        if (userId != currentUser?.id) {
          users.add(
            User(
              id: userId,
              email: 'Пользователь $userId', // Ограниченная информация
              role: 'user', // По умолчанию
              createdAt: DateTime.now(), // Приблизительная дата
              metadata: {'limited_info': true},
            ),
          );
        }
      }

      if (kDebugMode) {
        log(
          '✅ Создано ${users.length} записей пользователей (альтернативный метод)',
        );
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка альтернативного метода: $e');
      }
      throw Exception('Не удалось загрузить пользователей: $e');
    }
  }

  /// Получение прав конкретного пользователя (только для админов)
  Future<List<String>> getUserPermissions(String userId) async {
    if (!isCurrentUserAdmin) {
      throw Exception('Недостаточно прав для просмотра прав пользователя');
    }

    try {
      final response = await _supabaseService.client
          .from('user_permissions')
          .select('permission_name')
          .eq('user_id', userId);

      final permissions =
          (response as List)
              .map((item) => item['permission_name'] as String)
              .toList();

      if (kDebugMode) {
        log('✅ Загружены права пользователя $userId: $permissions');
      }

      return permissions;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки прав пользователя $userId: $e');
      }
      rethrow;
    }
  }

  /// Предоставление права пользователю (только для админов)
  Future<void> grantPermission(String userId, String permission) async {
    if (!isCurrentUserAdmin) {
      throw Exception('Недостаточно прав для предоставления прав');
    }

    try {
      await _supabaseService.client.from('user_permissions').insert({
        'user_id': userId,
        'permission_name': permission,
        'granted_by_user_id': _supabaseService.currentUserId,
      });

      if (kDebugMode) {
        log('✅ Предоставлено право $permission пользователю $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        log(
          '❌ Ошибка предоставления права $permission пользователю $userId: $e',
        );
      }
      rethrow;
    }
  }

  /// Отзыв права у пользователя (только для админов)
  Future<void> revokePermission(String userId, String permission) async {
    if (!isCurrentUserAdmin) {
      throw Exception('Недостаточно прав для отзыва прав');
    }

    try {
      await _supabaseService.client
          .from('user_permissions')
          .delete()
          .eq('user_id', userId)
          .eq('permission_name', permission);

      if (kDebugMode) {
        log('✅ Отозвано право $permission у пользователя $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка отзыва права $permission у пользователя $userId: $e');
      }
      rethrow;
    }
  }

  /// Обновление прав пользователя (только для админов)
  Future<void> updateUserPermissions(
    String userId,
    List<String> permissions,
  ) async {
    if (!isCurrentUserAdmin) {
      throw Exception('Недостаточно прав для обновления прав');
    }

    try {
      // Получаем текущие права
      final currentPermissions = await getUserPermissions(userId);

      // Определяем какие права нужно добавить
      final permissionsToAdd =
          permissions.where((p) => !currentPermissions.contains(p)).toList();

      // Определяем какие права нужно удалить
      final permissionsToRemove =
          currentPermissions.where((p) => !permissions.contains(p)).toList();

      // Добавляем новые права
      for (final permission in permissionsToAdd) {
        await grantPermission(userId, permission);
      }

      // Удаляем ненужные права
      for (final permission in permissionsToRemove) {
        await revokePermission(userId, permission);
      }

      if (kDebugMode) {
        log('✅ Обновлены права пользователя $userId');
        log('   Добавлено: $permissionsToAdd');
        log('   Удалено: $permissionsToRemove');
      }
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка обновления прав пользователя $userId: $e');
      }
      rethrow;
    }
  }

  /// Очистка кэша прав (вызывается при изменении прав)
  void clearPermissionsCache() {
    _cachedUserPermissions = null;
  }

  /// Инициализация сервиса (загрузка прав текущего пользователя)
  Future<void> initialize() async {
    if (_supabaseService.isAuthenticated) {
      await getCurrentUserPermissions();
    }
  }
}
