import 'dart:convert';
import 'dart:developer';

import 'package:flutter/foundation.dart';

import '../../core/services/cache_service.dart';
import '../../core/services/supabase_service.dart';
import 'news_item.dart';
import 'news_repository.dart';

/// Реализация NewsRepository с использованием Supabase
class SupabaseNewsRepository implements NewsRepository {
  final SupabaseService _supabaseService;
  final CacheService _cacheService;

  SupabaseNewsRepository(this._supabaseService, this._cacheService);

  @override
  Future<List<NewsItem>> getNews({bool forceRefresh = false}) async {
    try {
      // Если не требуется принудительное обновление, пытаемся получить из кэша
      if (!forceRefresh) {
        final cachedNews = await _getCachedNews();
        if (cachedNews != null) {
          if (kDebugMode) {
            log('📱 Новости загружены из кэша: ${cachedNews.length} записей');
          }
          return cachedNews;
        }
      }

      if (kDebugMode) {
        log('🌐 Загружаем новости из Supabase...');
      }

      // Загружаем из Supabase
      final response = await _supabaseService
          .from('news')
          .select('id, content, created_at')
          .order('created_at', ascending: false);

      final List<NewsItem> newsList =
          (response as List)
              .map((json) => NewsItem.fromJson(json as Map<String, dynamic>))
              .toList();

      // Кэшируем данные
      await _cacheNews(newsList);

      if (kDebugMode) {
        log('✅ Загружено ${newsList.length} новостей из Supabase');
      }

      return newsList;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка загрузки новостей из Supabase: $e');
      }

      // При ошибке пытаемся вернуть кэшированные данные
      final cachedNews = await _getCachedNews();
      if (cachedNews != null) {
        if (kDebugMode) {
          log('📱 Возвращаем кэшированные новости из-за ошибки');
        }
        return cachedNews;
      }

      rethrow;
    }
  }

  /// Подписка на Realtime обновления новостей
  @override
  Stream<List<NewsItem>> subscribeToNews() {
    return _supabaseService
        .from('news')
        .stream(primaryKey: ['id'])
        .order('created_at', ascending: false)
        .map((data) {
          final newsList =
              (data as List)
                  .map(
                    (json) => NewsItem.fromJson(json as Map<String, dynamic>),
                  )
                  .toList();

          // Обновляем кэш при получении новых данных
          _cacheNews(newsList);

          if (kDebugMode) {
            log('🔄 Realtime обновление новостей: ${newsList.length} записей');
          }

          return newsList;
        });
  }

  /// Получение кэшированных новостей
  Future<List<NewsItem>?> _getCachedNews() async {
    try {
      final cachedData = _cacheService.getNews();
      if (cachedData != null) {
        final List<dynamic> jsonList = jsonDecode(cachedData);
        return jsonList
            .map((json) => NewsItem.fromJson(json as Map<String, dynamic>))
            .toList();
      }
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка чтения кэша новостей: $e');
      }
      // Очищаем поврежденный кэш
      await _clearCache();
    }

    return null;
  }

  /// Кэширование новостей
  Future<void> _cacheNews(List<NewsItem> newsList) async {
    try {
      final jsonList = newsList.map((item) => item.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      await _cacheService.saveNews(jsonString);
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка кэширования новостей: $e');
      }
    }
  }

  /// Очистка кэша новостей
  Future<void> _clearCache() async {
    try {
      await _cacheService.clearNews();
    } catch (e) {
      if (kDebugMode) {
        log('⚠️ Ошибка очистки кэша новостей: $e');
      }
    }
  }

  /// Принудительное обновление кэша
  Future<void> refreshCache() async {
    await getNews(forceRefresh: true);
  }

  /// Создание новой новости
  @override
  Future<NewsItem> createNews(String content) async {
    try {
      if (kDebugMode) {
        log('🚀 Создание новости...');
      }

      final response =
          await _supabaseService.client
              .from('news')
              .insert({'content': content})
              .select('id, content, created_at')
              .single();

      final newsItem = NewsItem.fromJson(response);

      if (kDebugMode) {
        log('✅ Новость создана: ${newsItem.id}');
      }

      // Обновляем кэш
      await refreshCache();

      return newsItem;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка создания новости: $e');
      }
      rethrow;
    }
  }

  /// Обновление существующей новости
  @override
  Future<NewsItem> updateNews(int id, String content) async {
    try {
      if (kDebugMode) {
        log('🚀 Обновление новости $id...');
      }

      final response =
          await _supabaseService.client
              .from('news')
              .update({'content': content})
              .eq('id', id)
              .select('id, content, created_at')
              .single();

      final newsItem = NewsItem.fromJson(response);

      if (kDebugMode) {
        log('✅ Новость обновлена: ${newsItem.id}');
      }

      // Обновляем кэш
      await refreshCache();

      return newsItem;
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка обновления новости $id: $e');
      }
      rethrow;
    }
  }

  /// Удаление новости
  @override
  Future<void> deleteNews(int id) async {
    try {
      if (kDebugMode) {
        log('🚀 Удаление новости $id...');
      }

      await _supabaseService.client.from('news').delete().eq('id', id);

      if (kDebugMode) {
        log('✅ Новость удалена: $id');
      }

      // Обновляем кэш
      await refreshCache();
    } catch (e) {
      if (kDebugMode) {
        log('❌ Ошибка удаления новости $id: $e');
      }
      rethrow;
    }
  }
}
