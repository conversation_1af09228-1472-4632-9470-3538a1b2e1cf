import 'package:luxury_app/features/news/news_item.dart';

/// Интерфейс репозитория для работы с новостями
abstract class NewsRepository {
  /// Получение списка всех новостей
  ///
  /// Параметр [forceRefresh] указывает, нужно ли принудительно обновить данные
  Future<List<NewsItem>> getNews({bool forceRefresh = false});

  /// Подписка на real-time обновления новостей
  Stream<List<NewsItem>> subscribeToNews();

  /// Создание новой новости
  Future<NewsItem> createNews(String content);

  /// Обновление существующей новости
  Future<NewsItem> updateNews(int id, String content);

  /// Удаление новости
  Future<void> deleteNews(int id);
}
