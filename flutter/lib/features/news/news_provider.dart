import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/models/data_state.dart';
import 'package:luxury_app/core/services/app_preload_service.dart';

import 'news_item.dart';
import 'news_repository.dart';

/// Нотификатор для управления состоянием новостей
class NewsNotifier extends StateNotifier<DataState<List<NewsItem>>> {
  final NewsRepository _newsRepository;
  final AppPreloadService _preloadService;
  StreamSubscription<List<NewsItem>>? _newsSubscription;

  NewsNotifier(this._newsRepository, this._preloadService)
    : super(const DataState<List<NewsItem>>()) {
    _initializeNews();
  }

  @override
  void dispose() {
    _newsSubscription?.cancel();
    super.dispose();
  }

  /// Инициализация новостей с проверкой предзагруженных данных
  Future<void> _initializeNews() async {
    // Сначала проверяем, есть ли предзагруженные данные
    final preloadedNews = await _preloadService.getPreloadedNews();

    if (preloadedNews != null && preloadedNews.isNotEmpty) {
      if (kDebugMode) {
        debugPrint(
          '📰 [NewsProvider] Используем предзагруженные новости: ${preloadedNews.length} записей',
        );
      }

      // Устанавливаем предзагруженные данные без показа спиннера
      state = state.copyWithData(preloadedNews, source: DataSource.cache);

      // Подписываемся на обновления в фоне
      _subscribeToNewsUpdates();
    } else {
      // Если предзагруженных данных нет, загружаем обычным способом
      if (kDebugMode) {
        debugPrint(
          '📰 [NewsProvider] Предзагруженных данных нет, загружаем обычным способом',
        );
      }
      _subscribeToNews();
    }
  }

  /// Подписка на real-time обновления новостей (без показа спиннера)
  void _subscribeToNewsUpdates() {
    try {
      _newsSubscription = _newsRepository.subscribeToNews().listen(
        (newsList) {
          if (kDebugMode) {
            debugPrint(
              '📰 [NewsProvider] Получены обновления новостей: ${newsList.length} записей',
            );
          }
          state = state.copyWithData(newsList, source: DataSource.network);
        },
        onError: (error) {
          if (kDebugMode) {
            debugPrint('❌ [NewsProvider] Ошибка обновления новостей: $error');
          }
          // Не показываем ошибку если у нас уже есть данные
          if (state.data == null || state.data!.isEmpty) {
            state = state.copyWithError('Ошибка загрузки новостей: $error');
          }
        },
      );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ [NewsProvider] Ошибка подписки на обновления: $e');
      }
      // Не показываем ошибку если у нас уже есть данные
      if (state.data == null || state.data!.isEmpty) {
        state = state.copyWithError('Ошибка подписки на новости: $e');
      }
    }
  }

  /// Подписка на real-time обновления новостей (с показом спиннера)
  void _subscribeToNews() {
    state = state.copyWithLoading();

    try {
      _newsSubscription = _newsRepository.subscribeToNews().listen(
        (newsList) {
          state = state.copyWithData(newsList, source: DataSource.network);
        },
        onError: (error) {
          state = state.copyWithError('Ошибка загрузки новостей: $error');
        },
      );
    } catch (e) {
      state = state.copyWithError('Ошибка подписки на новости: $e');
    }
  }

  /// Принудительное обновление (для RefreshIndicator)
  Future<void> refreshNews() async {
    try {
      state = state.copyWithLoading();
      final newsList = await _newsRepository.getNews(forceRefresh: true);
      state = state.copyWithData(newsList, source: DataSource.network);
    } catch (e) {
      state = state.copyWithError('Ошибка обновления новостей: $e');
    }
  }

  /// Создание новой новости
  Future<NewsItem> createNews(String content) async {
    try {
      final newsItem = await _newsRepository.createNews(content);
      // Обновляем состояние после создания
      await refreshNews();
      return newsItem;
    } catch (e) {
      state = state.copyWithError('Ошибка создания новости: $e');
      rethrow;
    }
  }

  /// Обновление существующей новости
  Future<NewsItem> updateNews(int id, String content) async {
    try {
      final newsItem = await _newsRepository.updateNews(id, content);
      // Обновляем состояние после обновления
      await refreshNews();
      return newsItem;
    } catch (e) {
      state = state.copyWithError('Ошибка обновления новости: $e');
      rethrow;
    }
  }

  /// Удаление новости
  Future<void> deleteNews(int id) async {
    try {
      await _newsRepository.deleteNews(id);
      // Обновляем состояние после удаления
      await refreshNews();
    } catch (e) {
      state = state.copyWithError('Ошибка удаления новости: $e');
      rethrow;
    }
  }
}

/// Провайдер для управления состоянием новостей
final newsProvider =
    StateNotifierProvider<NewsNotifier, DataState<List<NewsItem>>>((ref) {
      return NewsNotifier(
        ref.read(newsRepositoryProvider),
        ref.read(appPreloadServiceProvider),
      );
    });
