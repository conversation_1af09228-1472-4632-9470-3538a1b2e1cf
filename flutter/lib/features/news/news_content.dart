import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/features/news/news_item.dart';
import 'package:luxury_app/features/news/news_provider.dart';
import 'package:luxury_app/features/news/widgets/news_inline_editor.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/utils/date_formatter.dart';
import 'package:luxury_app/shared/widgets/markdown_renderer.dart';
import 'package:luxury_app/shared/widgets/state_widgets.dart';

/// Виджет для отображения новостей
class NewsContent extends ConsumerStatefulWidget {
  const NewsContent({super.key});

  @override
  ConsumerState<NewsContent> createState() => _NewsContentState();
}

class _NewsContentState extends ConsumerState<NewsContent> {
  @override
  Widget build(BuildContext context) {
    final newsState = ref.watch(newsProvider);

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Center(
        child: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 800),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              DataStateWidget<List<NewsItem>>(
                isLoading: newsState.isLoading,
                error: newsState.error,
                data: newsState.data,
                onRetry: () => ref.read(newsProvider.notifier).refreshNews(),
                emptyMessage: 'Нет новостей',
                dataBuilder:
                    (newsList) =>
                        _buildNewsList(context, newsList, newsState.isLoading),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNewsList(
    BuildContext context,
    List<NewsItem> newsList,
    bool isLoading,
  ) {
    return Consumer(
      builder: (context, ref, child) {
        final editingState = ref.watch(newsEditingProvider);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (isLoading) const LinearProgressIndicator(),
            const SizedBox(height: 16),

            // Форма создания новой новости (если активна)
            if (editingState.isCreatingNew) ...[
              const NewsInlineEditor(newsId: null, initialContent: ''),
              const Divider(height: 48),
            ],

            // Список существующих новостей
            for (int i = 0; i < newsList.length; i++) ...[
              _buildNewsItem(context, newsList[i]),
              if (i < newsList.length - 1) const Divider(height: 48),
            ],
          ],
        );
      },
    );
  }

  Widget _buildNewsItem(BuildContext context, NewsItem newsItem) {
    return Consumer(
      builder: (context, ref, child) {
        final permissionService = ref.watch(permissionServiceProvider);
        final editingState = ref.watch(newsEditingProvider);
        final canEdit = permissionService.hasPermission(
          Permissions.canEditNews,
        );
        final canDelete = permissionService.hasPermission(
          Permissions.canDeleteNews,
        );

        // Проверяем, редактируется ли эта новость
        final isEditing = editingState.isEditingNews(newsItem.id);

        if (isEditing) {
          // Показываем inline редактор
          return NewsInlineEditor(
            newsId: newsItem.id,
            initialContent: newsItem.content,
          );
        }

        // Показываем обычное отображение новости
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: MarkdownRenderer(
                    data: newsItem.content,
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                  ),
                ),
                // Кнопки управления новостью
                if (canEdit || canDelete)
                  Padding(
                    padding: const EdgeInsets.only(left: AppSizes.paddingS),
                    child: Column(
                      children: [
                        if (canEdit)
                          IconButton(
                            onPressed: () => _startEditingNews(newsItem),
                            icon: const Icon(LucideIcons.edit, size: 16),
                            tooltip: 'Редактировать',
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                          ),
                        if (canDelete)
                          IconButton(
                            onPressed:
                                () => _showDeleteNewsDialog(context, newsItem),
                            icon: const Icon(LucideIcons.trash, size: 16),
                            tooltip: 'Удалить',
                            constraints: const BoxConstraints(
                              minWidth: 32,
                              minHeight: 32,
                            ),
                          ),
                      ],
                    ),
                  ),
              ],
            ),
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: Text(
                  DateFormatter.formatDateTime(newsItem.createdAt),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withAlpha(150),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Начать редактирование новости
  void _startEditingNews(NewsItem newsItem) {
    ref
        .read(newsEditingProvider.notifier)
        .startEditingNews(newsItem.id, newsItem.content);
  }

  void _showDeleteNewsDialog(BuildContext context, NewsItem newsItem) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Удалить новость?'),
            content: const Text('Это действие нельзя отменить.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  try {
                    await ref
                        .read(newsProvider.notifier)
                        .deleteNews(newsItem.id);
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Новость удалена'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Ошибка удаления новости: $e'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
                child: const Text('Удалить'),
              ),
            ],
          ),
    );
  }
}
