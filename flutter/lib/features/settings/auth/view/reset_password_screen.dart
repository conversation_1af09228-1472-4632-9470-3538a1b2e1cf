import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:luxury_app/shared/utils/keychain_helper.dart';
import 'package:luxury_app/features/settings/auth/auth_repository/supabase_auth_repository.dart';

class ResetPasswordScreen extends StatefulWidget {
  final String token;
  final String email;
  const ResetPasswordScreen({super.key, required this.token, required this.email});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _passwordController = TextEditingController();
  bool _loading = false;
  String? _error;

  @override
  void dispose() {
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() { _loading = true; _error = null; });
    try {
      await SupabaseAuthRepository.instance.verifyPasswordReset(
        email: widget.email,
        token: widget.token,
        newPassword: _passwordController.text,
      );
      await KeychainHelper.savePassword(widget.email, _passwordController.text);
      if (mounted) context.go('/settings');
    } catch (e) {
      setState(() { _error = e.toString(); });
    } finally {
      setState(() { _loading = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Сброс пароля')),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('Введите новый пароль для аккаунта ${widget.email}'),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                obscureText: true,
                decoration: const InputDecoration(labelText: 'Новый пароль'),
                validator: (v) => v != null && v.length >= 8 ? null : 'Минимум 8 символов',
              ),
              const SizedBox(height: 16),
              if (_error != null) Text(_error!, style: const TextStyle(color: Colors.red)),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loading ? null : _submit,
                child: _loading ? const CircularProgressIndicator() : const Text('Сменить пароль'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
