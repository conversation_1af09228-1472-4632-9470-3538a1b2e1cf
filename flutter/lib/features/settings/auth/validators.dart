String? validatePassword(String? value, {int minLength = 8}) {
  if (value == null || value.isEmpty) {
    return 'Введите пароль';
  }
  if (value.length < minLength) {
    return 'Пароль должен быть не короче $minLength символов';
  }
  return null;
}

String? validateEmail(String? value) {
  if (value == null || value.isEmpty) {
    return 'Введите email';
  }
  final emailRegExp = RegExp(r'^[\w-.]+@[\w-]+\.[a-zA-Z]{2,} ?$');
  if (!emailRegExp.hasMatch(value)) {
    return 'Введите корректный email';
  }
  return null;
}

String? validatePhone(String? value) {
  if (value == null || value.isEmpty) return null;
  final phoneRegExp = RegExp(r'^[0-9+\-() ]{6,}$');
  if (!phoneRegExp.hasMatch(value)) {
    return 'Введите корректный телефон';
  }
  return null;
}
