import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/core/services/cache_service.dart';
import 'package:luxury_app/core/services/simple_audio_service.dart';
import 'package:luxury_app/features/audio/audio_button_widget.dart';
import 'package:luxury_app/features/wiki/wiki_provider.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/widgets/markdown_renderer.dart';

/// Виджет для отображения содержимого вики-статьи в формате Markdown
class WikiContent extends ConsumerStatefulWidget {
  final String fileId;
  final String? fileName;

  const WikiContent({super.key, required this.fileId, this.fileName});

  @override
  ConsumerState<WikiContent> createState() => _WikiContentState();
}

class _WikiContentState extends ConsumerState<WikiContent>
    with AutomaticKeepAliveClientMixin, LoggerMixin {
  final ScrollController _scrollController = ScrollController();

  // Кешируем последнее загруженное состояние, чтобы избежать мерцания при перестроении
  String? _cachedFileContent;
  bool _cachedHasAudio = false;
  String? _cachedAudioUrl;

  // --- scroll position ---
  bool _restoringScroll = false;
  bool _hasInitialized = false;
  Timer? _scrollRestoreTimer;

  void _onScrollChanged() {
    if (_restoringScroll || !mounted) return;
    if (!_scrollController.hasClients) return;

    final offset = _scrollController.offset;
    if (offset < 0) return;

    try {
      CacheService.instance.saveWikiScrollPosition(widget.fileId, offset);
    } catch (e) {
      logError('Ошибка при сохранении позиции скролла: $e');
    }
  }

  void _restoreScrollPositionAfterImagesLoaded() async {
    if (!mounted || _restoringScroll) return;

    _restoringScroll = true;
    try {
      await CacheService.instance.init();
      final offset = CacheService.instance.getWikiScrollPosition(widget.fileId) ?? 0.0;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _scrollController.hasClients && offset > 0) {
          try {
            Future.delayed(const Duration(milliseconds: 100), () async {
              if (mounted && _scrollController.hasClients) {
                final maxScrollExtent = _scrollController.position.maxScrollExtent;
                final targetOffset = offset.clamp(0.0, maxScrollExtent);
                try {
                  await _scrollController.animateTo(
                    targetOffset,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                } catch (e) {
                  logError('Ошибка анимации скролла: $e');
                } finally {
                  _restoringScroll = false;
                }
              } else {
                _restoringScroll = false;
              }
            });
          } catch (e) {
            logError('Ошибка восстановления позиции скролла: $e');
            _restoringScroll = false;
          }
        } else {
          _restoringScroll = false;
        }
      });
    } catch (e) {
      logError('Ошибка при инициализации кеша: $e');
      _restoringScroll = false;
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    // Подписываемся на изменения скролла
    _scrollController.addListener(_onScrollChanged);
  }

  @override
  void didUpdateWidget(WikiContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Если изменен fileId, сбрасываем флаг восстановления скролла
    if (widget.fileId != oldWidget.fileId) {
      _restoringScroll = false;
      _hasInitialized = false;

      // Примечание: Убрали автоматическую остановку аудио при смене файла
      // Теперь аудио будет продолжать играть в фоновом режиме
    }
  }

  /// Инициализирует загрузку файла при первом построении
  void _initializeFileLoad() {
    if (_hasInitialized) return;
    _hasInitialized = true;

    logInfo(
      '🔄 WikiContent _initializeFileLoad: Начинаем загрузку файла ${widget.fileId}',
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        try {
          final wikiState = ref.read(wikiProvider);

          // Если файл уже загружен и это тот же самый файл - ничего не делаем
          if (wikiState.loadedFileId == widget.fileId &&
              wikiState.fileContent != null) {
            logInfo(
              '📱 WikiContent _initializeFileLoad: Файл уже загружен, используем кеш',
            );
            // Кешируем состояние
            _cachedFileContent = wikiState.fileContent?['markdown'] as String?;
            _cachedHasAudio = wikiState.hasAudio;
            _cachedAudioUrl = wikiState.audioUrl;
            return;
          }

          // Если файл не загружен или это другой файл - загружаем новый контент
          logInfo(
            '🌐 WikiContent _initializeFileLoad: Загружаем новый файл ${widget.fileId}',
          );
          ref.read(wikiProvider.notifier).loadFile(widget.fileId);
        } catch (e) {
          logError('Ошибка при инициализации загрузки файла: $e');
        }
      }
    });
  }

  @override
  void dispose() {
    // Отменяем таймер восстановления скролла
    _scrollRestoreTimer?.cancel();

    // Удаляем слушатель скролла
    _scrollController.removeListener(_onScrollChanged);

    // Освобождаем контроллер скролла
    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Необходимо для AutomaticKeepAliveClientMixin

    // Инициализируем загрузку файла при первом построении
    _initializeFileLoad();

    return Consumer(
      builder: (context, ref, child) {
        final wikiState = ref.watch(wikiProvider);

        logInfo(
          '🔍 WikiContent build: fileId=${widget.fileId}, isLoading=${wikiState.isLoading}, hasFileLoaded=${wikiState.hasFileLoaded}, loadedFileId=${wikiState.loadedFileId}, hasError=${wikiState.hasError}',
        );

        // Если идет загрузка и у нас есть кешированный контент для текущего файла
        if (wikiState.isLoading && _cachedFileContent != null) {
          logInfo(
            '🔄 WikiContent: Показываем кешированный контент во время загрузки',
          );
          return _buildContentView(
            _cachedFileContent!,
            _cachedHasAudio,
            _cachedAudioUrl,
          );
        }

        // Если файл загружен и это нужный файл
        if (wikiState.hasFileLoaded &&
            wikiState.loadedFileId == widget.fileId) {
          logInfo('✅ WikiContent: Показываем загруженный контент');
          _cachedFileContent = wikiState.fileContent?['markdown'] as String?;
          _cachedHasAudio = wikiState.hasAudio;
          _cachedAudioUrl = wikiState.audioUrl;

          // Восстанавливаем скролл после первой загрузки
          if (!_restoringScroll) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _restoreScrollPositionAfterImagesLoaded();
            });
          }

          return _buildContentView(
            wikiState.fileContent?['markdown'] as String? ?? '',
            wikiState.hasAudio,
            wikiState.audioUrl,
          );
        }

        // Если есть ошибка
        if (wikiState.hasError) {
          logError('❌ WikiContent: Ошибка загрузки - ${wikiState.error}');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Theme.of(context).colorScheme.error,
                ),
                const SizedBox(height: 16),
                Text(
                  'Ошибка загрузки статьи',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  wikiState.error ?? 'Неизвестная ошибка',
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    ref.read(wikiProvider.notifier).loadFile(widget.fileId);
                  },
                  child: const Text('Повторить'),
                ),
              ],
            ),
          );
        }

        // Показываем индикатор загрузки с информативным сообщением
        logInfo('⏳ WikiContent: Показываем индикатор загрузки');
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Загрузка статьи...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Создает отображение содержимого файла
  Widget _buildContentView(String content, bool hasAudio, String? audioUrl) {
    logInfo(
      '📝 WikiContent _buildContentView: контент длиной ${content.length} символов',
    );

    return StreamBuilder<SimpleAudioState>(
      stream: SimpleAudioService.instance.stateStream,
      initialData: SimpleAudioService.instance.currentState,
      builder: (context, snapshot) {
        final audioState = snapshot.data!;

        // Показываем кнопку воспроизведения только если аудио не играет или играет другой файл
        final showAudioButton =
            hasAudio &&
            (audioState.currentFileId != widget.fileId ||
                !audioState.showPlayer);

        return SingleChildScrollView(
          controller: _scrollController,
          // Добавляем cacheExtent для лучшей производительности

          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Кнопка воспроизведения аудио
              if (showAudioButton)
                Padding(
                  padding: const EdgeInsets.all(AppSizes.paddingM),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: AudioButtonWidget(
                      fileId: widget.fileId,
                      onPressed: () {
                        if (audioUrl != null && audioUrl.isNotEmpty) {
                          try {
                            SimpleAudioService.instance.playAudio(
                              fileId: widget.fileId,
                              audioUrl: audioUrl,
                              sourcePageId: widget.fileId,
                              sourcePageTitle: widget.fileName,
                              forceRefresh: true,
                            );
                          } catch (e) {
                            logError('Ошибка при воспроизведении аудио: $e');
                          }
                        }
                      },
                    ),
                  ),
                ),

              // Содержимое Markdown с RepaintBoundary для лучшей производительности
              RepaintBoundary(
                child: MarkdownRenderer(
                  data: content,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  onAllImagesLoaded: _restoreScrollPositionAfterImagesLoaded,
                ),
              ),

              // Отступ для глобального плеера
              const SizedBox(height: 200),
            ],
          ),
        );
      },
    );
  }
}
