import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';
import 'package:luxury_app/navigation/route_config.dart';
import 'package:luxury_app/shared/constants/sizes.dart';

/// Экран со списком пользователей (только для админов)
class UsersListScreen extends ConsumerStatefulWidget {
  /// Ключ Scaffold для закрытия drawer на мобильных устройствах
  final GlobalKey<ScaffoldState>? scaffoldKey;

  const UsersListScreen({super.key, this.scaffoldKey});

  @override
  ConsumerState<UsersListScreen> createState() => _UsersListScreenState();
}

class _UsersListScreenState extends ConsumerState<UsersListScreen> {
  List<User>? _users;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final permissionService = ref.read(permissionServiceProvider);
      final users = await permissionService.getAllUsers();

      setState(() {
        _users = users;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final permissionService = ref.watch(permissionServiceProvider);

    // Проверяем права доступа
    if (!permissionService.isCurrentUserAdmin) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(LucideIcons.lock, size: 48, color: Colors.orange),
            const SizedBox(height: AppSizes.paddingM),
            const Text(
              'Недостаточно прав для просмотра пользователей',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              'Только администраторы могут управлять пользователями.',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.paddingL),
            ElevatedButton(
              onPressed: () {
                // Обновляем права пользователя
                permissionService.clearPermissionsCache();
                permissionService.initialize();
                setState(() {}); // Обновляем UI
              },
              child: const Text('Проверить права снова'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Заголовок с кнопками
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Row(
            children: [
              const Icon(LucideIcons.users, size: 24),
              const SizedBox(width: AppSizes.paddingS),
              const Text(
                'Управление пользователями',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(LucideIcons.refreshCw),
                onPressed: _loadUsers,
                tooltip: 'Обновить список',
              ),
            ],
          ),
        ),
        const Divider(height: 1),

        // Содержимое
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      // Определяем тип ошибки для показа соответствующего сообщения
      String errorMessage = 'Ошибка загрузки: $_error';
      IconData errorIcon = LucideIcons.alertCircle;
      Color errorColor = Colors.red;

      if (_error!.contains('permission denied') ||
          _error!.contains('Недостаточно прав')) {
        errorMessage =
            'У вас недостаточно прав для просмотра пользователей. Обратитесь к администратору.';
        errorIcon = LucideIcons.lock;
        errorColor = Colors.orange;
      } else if (_error!.contains('network') ||
          _error!.contains('connection')) {
        errorMessage =
            'Ошибка подключения к серверу. Проверьте интернет-соединение.';
        errorIcon = LucideIcons.wifiOff;
        errorColor = Colors.blue;
      }

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(errorIcon, size: 48, color: errorColor),
            const SizedBox(height: AppSizes.paddingM),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSizes.paddingL,
              ),
              child: Text(
                errorMessage,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: AppSizes.paddingM),
            ElevatedButton(
              onPressed: _loadUsers,
              child: const Text('Попробовать снова'),
            ),
            if (_error!.contains('permission denied') ||
                _error!.contains('Недостаточно прав')) ...[
              const SizedBox(height: AppSizes.paddingS),
              TextButton(
                onPressed: () {
                  // Показываем детали ошибки
                  showDialog(
                    context: context,
                    builder:
                        (context) => AlertDialog(
                          title: const Text('Детали ошибки'),
                          content: SingleChildScrollView(child: Text(_error!)),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Закрыть'),
                            ),
                          ],
                        ),
                  );
                },
                child: const Text('Показать детали ошибки'),
              ),
            ],
          ],
        ),
      );
    }

    if (_users == null || _users!.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(LucideIcons.users, size: 48, color: Colors.grey),
            SizedBox(height: AppSizes.paddingM),
            Text('Пользователи не найдены', style: TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      itemCount: _users!.length,
      itemBuilder: (context, index) {
        final user = _users![index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppSizes.paddingS),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Text(
            user.email.isNotEmpty ? user.email[0].toUpperCase() : '?',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          user.email,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${user.id}'),
            const SizedBox(height: 4),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color:
                        user.role == 'admin'
                            ? Colors.red.withValues(alpha: 0.1)
                            : Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.role == 'admin' ? 'Администратор' : 'Пользователь',
                    style: TextStyle(
                      fontSize: 12,
                      color: user.role == 'admin' ? Colors.red : Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  'Создан: ${_formatDate(user.createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                ),
              ],
            ),
          ],
        ),
        trailing: const Icon(LucideIcons.chevronRight),
        onTap: () => _openUserPermissions(user),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year}';
  }

  void _openUserPermissions(User user) {
    // Закрываем drawer на мобильных устройствах
    if (widget.scaffoldKey?.currentState?.isDrawerOpen == true) {
      widget.scaffoldKey!.currentState!.closeDrawer();
    }

    // Отладочная информация
    debugPrint('🔄 Переходим к правам пользователя:');
    debugPrint('   User ID: ${user.id}');
    debugPrint('   User Email: ${user.email}');
    debugPrint('   Route: ${RouteConfig.userPermissionsRoute}');
    debugPrint('   Path Parameters: userId=${user.id}');

    // Переходим на экран управления правами пользователя
    try {
      context.pushNamed(
        RouteConfig.userPermissionsRoute,
        pathParameters: {'userId': user.id},
        extra: user,
      );
      debugPrint('✅ Навигация успешно запущена');
    } catch (e) {
      debugPrint('❌ Ошибка навигации: $e');
    }
  }
}
