import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/services/permission_service.dart';
import 'package:luxury_app/features/settings/auth/auth_models/user.dart';
import 'package:luxury_app/shared/constants/sizes.dart';

/// Экран управления правами конкретного пользователя
class UserPermissionsScreen extends ConsumerStatefulWidget {
  /// Пользователь, права которого редактируются
  final User user;

  const UserPermissionsScreen({super.key, required this.user});

  @override
  ConsumerState<UserPermissionsScreen> createState() =>
      _UserPermissionsScreenState();
}

class _UserPermissionsScreenState extends ConsumerState<UserPermissionsScreen> {
  List<String> _userPermissions = [];
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Состояние чекбоксов
  final Map<String, bool> _permissionStates = {};

  @override
  void initState() {
    super.initState();

    // Отладочная информация
    if (kDebugMode) {
      debugPrint('🔄 UserPermissionsScreen initState:');
      debugPrint('   User ID: ${widget.user.id}');
      debugPrint('   User Email: ${widget.user.email}');
      debugPrint('   User Role: ${widget.user.role}');
    }

    _loadUserPermissions();
  }

  Future<void> _loadUserPermissions() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      if (kDebugMode) {
        debugPrint('🔄 Загружаем права пользователя ${widget.user.id}...');
      }

      final permissionService = ref.read(permissionServiceProvider);
      final permissions = await permissionService.getUserPermissions(
        widget.user.id,
      );

      if (kDebugMode) {
        debugPrint('✅ Загружены права: $permissions');
      }

      setState(() {
        _userPermissions = permissions;
        _isLoading = false;

        // Инициализируем состояние чекбоксов
        _permissionStates.clear();
        for (final permission in Permissions.all) {
          _permissionStates[permission] = permissions.contains(permission);
        }
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка загрузки прав пользователя: $e');
        debugPrint('   User ID: ${widget.user.id}');
        debugPrint('   Error type: ${e.runtimeType}');
      }

      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _savePermissions() async {
    try {
      setState(() {
        _isSaving = true;
        _error = null;
      });

      final permissionService = ref.read(permissionServiceProvider);

      // Собираем список активных прав
      final activePermissions =
          _permissionStates.entries
              .where((entry) => entry.value)
              .map((entry) => entry.key)
              .toList();

      await permissionService.updateUserPermissions(
        widget.user.id,
        activePermissions,
      );

      setState(() {
        _isSaving = false;
      });

      // Показываем уведомление об успехе
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Права пользователя успешно обновлены'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isSaving = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Ошибка сохранения: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final permissionService = ref.watch(permissionServiceProvider);

    // Проверяем права доступа
    if (!permissionService.isCurrentUserAdmin) {
      return const Center(
        child: Text(
          'Недостаточно прав для управления пользователями',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return Column(
      children: [
        // Кнопки действий
        Container(
          padding: const EdgeInsets.all(AppSizes.paddingM),
          child: Row(
            children: [
              IconButton(
                icon: const Icon(LucideIcons.arrowLeft),
                onPressed: () => context.pop(),
                tooltip: 'Назад',
              ),
              const SizedBox(width: AppSizes.paddingS),
              Expanded(
                child: Text(
                  'Права пользователя: ${widget.user.email}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (_isSaving)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                ElevatedButton(
                  onPressed: _hasChanges() ? _savePermissions : null,
                  child: const Text('Сохранить'),
                ),
            ],
          ),
        ),
        const Divider(height: 1),

        // Содержимое
        Expanded(child: _buildContent()),
      ],
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(LucideIcons.alertCircle, size: 48, color: Colors.red),
            const SizedBox(height: AppSizes.paddingM),
            Text(
              'Ошибка загрузки: $_error',
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppSizes.paddingM),
            ElevatedButton(
              onPressed: _loadUserPermissions,
              child: const Text('Попробовать снова'),
            ),
          ],
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(AppSizes.paddingM),
      children: [
        // Информация о пользователе
        _buildUserInfo(),
        const SizedBox(height: AppSizes.paddingL),

        // Права пользователя
        _buildPermissionsSection(),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Информация о пользователе',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSizes.paddingM),
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Text(
                    widget.user.email.isNotEmpty
                        ? widget.user.email[0].toUpperCase()
                        : '?',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                ),
                const SizedBox(width: AppSizes.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.user.email,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'ID: ${widget.user.id}',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color:
                              widget.user.role == 'admin'
                                  ? Colors.red.withValues(alpha: 0.1)
                                  : Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          widget.user.role == 'admin'
                              ? 'Администратор'
                              : 'Пользователь',
                          style: TextStyle(
                            fontSize: 12,
                            color:
                                widget.user.role == 'admin'
                                    ? Colors.red
                                    : Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppSizes.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Права доступа',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: AppSizes.paddingS),
            Text(
              'Отметьте права, которые должны быть у пользователя:',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: AppSizes.paddingM),

            // Группировка прав по категориям
            _buildPermissionGroup('Новости', [
              Permissions.canCreateNews,
              Permissions.canEditNews,
              Permissions.canDeleteNews,
            ]),
            const SizedBox(height: AppSizes.paddingM),

            _buildPermissionGroup('Wiki', [
              Permissions.canCreateWiki,
              Permissions.canEditWiki,
              Permissions.canDeleteWiki,
              Permissions.canApproveWikiChanges,
            ]),
            const SizedBox(height: AppSizes.paddingM),

            _buildPermissionGroup('Аудио', [Permissions.canManageAudio]),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionGroup(String title, List<String> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: AppSizes.paddingS),
        ...permissions.map(
          (permission) => _buildPermissionCheckbox(permission),
        ),
      ],
    );
  }

  Widget _buildPermissionCheckbox(String permission) {
    final displayName = Permissions.displayNames[permission] ?? permission;
    final isChecked = _permissionStates[permission] ?? false;

    return CheckboxListTile(
      title: Text(displayName),
      value: isChecked,
      onChanged:
          _isSaving
              ? null
              : (value) {
                setState(() {
                  _permissionStates[permission] = value ?? false;
                });
              },
      contentPadding: EdgeInsets.zero,
      controlAffinity: ListTileControlAffinity.leading,
    );
  }

  bool _hasChanges() {
    for (final permission in Permissions.all) {
      final currentState = _permissionStates[permission] ?? false;
      final originalState = _userPermissions.contains(permission);
      if (currentState != originalState) {
        return true;
      }
    }
    return false;
  }
}
