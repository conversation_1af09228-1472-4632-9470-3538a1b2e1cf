import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/features/drawer/app_logo.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/theme/theme.dart';

class AuthScreen extends ConsumerStatefulWidget {
  const AuthScreen({super.key});

  @override
  ConsumerState<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends ConsumerState<AuthScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PageController _pageController;

  final _formKeys = [
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
    GlobalKey<FormState>(),
  ];

  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  final _forgotEmailController = TextEditingController();

  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;
  int _currentPageIndex = 0;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _pageController = PageController();

    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        _pageController.animateToPage(
          _tabController.index,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pageController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();
    _forgotEmailController.dispose();
    super.dispose();
  }

  void _clearError() {
    if (_errorMessage != null) {
      setState(() {
        _errorMessage = null;
      });
    }
  }

  Future<void> _handleLogin() async {
    _clearError();
    if (!_formKeys[0].currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authProvider.notifier)
          .login(_emailController.text.trim(), _passwordController.text);
      // Роутер автоматически перенаправит на главную страницу
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleRegister() async {
    _clearError();
    if (!_formKeys[1].currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authProvider.notifier)
          .register(
            _emailController.text.trim(),
            _passwordController.text,
            phone:
                _phoneController.text.trim().isEmpty
                    ? null
                    : _phoneController.text.trim(),
          );
      // После регистрации сразу логинимся
      await ref
          .read(authProvider.notifier)
          .login(_emailController.text.trim(), _passwordController.text);
      // Роутер автоматически перенаправит на главную страницу
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleForgotPassword() async {
    _clearError();
    if (!_formKeys[2].currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await ref
          .read(authProvider.notifier)
          .resetPassword(_forgotEmailController.text.trim());

      if (mounted) {
        _showSuccessMessage('Ссылка для сброса пароля отправлена на email');
        _switchToLogin();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _switchToLogin() {
    _tabController.animateTo(0);
    _clearControllers();
  }

  void _clearControllers() {
    _emailController.clear();
    _passwordController.clear();
    _confirmPasswordController.clear();
    _phoneController.clear();
    _forgotEmailController.clear();
  }

  String _generatePassword() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#%^&*';
    final random = DateTime.now().millisecondsSinceEpoch;
    final password =
        List.generate(16, (index) {
          return chars[(random + index) % chars.length];
        }).join();

    return password;
  }

  void _useGeneratedPassword() {
    final generatedPassword = _generatePassword();
    _passwordController.text = generatedPassword;
    if (_currentPageIndex == 1) {
      _confirmPasswordController.text = generatedPassword;
    }

    Clipboard.setData(ClipboardData(text: generatedPassword));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Пароль сгенерирован и скопирован в буфер обмена'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.brandPurple.withValues(alpha: 0.1),
              Theme.of(context).scaffoldBackgroundColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(AppSizes.paddingL),
              child: ConstrainedBox(
                constraints: const BoxConstraints(maxWidth: 400),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildHeader(),
                    SizedBox(height: AppSizes.paddingXL),
                    _buildTabBar(),
                    SizedBox(height: AppSizes.paddingL),
                    _buildContent(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 12),
        const AppLogo(),
        const SizedBox(height: 28),
        Text(
          'Добро пожаловать',
          style: Theme.of(
            context,
          ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Text(
          'Войдите в аккаунт для доступа к приложению',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(10),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Theme.of(context).colorScheme.onPrimary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurface,
        tabs: const [
          Tab(text: 'Вход'),
          Tab(text: 'Регистрация'),
          Tab(text: 'Сброс'),
        ],
        onTap: (index) {
          setState(() {
            _currentPageIndex = index;
            _errorMessage = null;
          });
        },
      ),
    );
  }

  Widget _buildContent() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(AppSizes.paddingL),
        child: Column(
          children: [
            if (_errorMessage != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(AppSizes.paddingM),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(
                      context,
                    ).colorScheme.error.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      LucideIcons.alertCircle,
                      color: Theme.of(context).colorScheme.error,
                      size: 20,
                    ),
                    SizedBox(width: AppSizes.paddingS),
                    Expanded(
                      child: Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.error,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: AppSizes.paddingM),
            ],
            SizedBox(
              height: 300,
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentPageIndex = index;
                    _errorMessage = null;
                  });
                  _tabController.animateTo(index);
                },
                children: [
                  _buildLoginForm(),
                  _buildRegisterForm(),
                  _buildForgotPasswordForm(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginForm() {
    return Form(
      key: _formKeys[0],
      child: Column(
        children: [
          _buildEmailField(),
          SizedBox(height: AppSizes.paddingM),
          _buildPasswordField(
            controller: _passwordController,
            obscure: _obscurePassword,
            onToggle:
                () => setState(() => _obscurePassword = !_obscurePassword),
            showGenerator: false,
          ),
          SizedBox(height: AppSizes.paddingL),
          _buildActionButton('Войти', _handleLogin, LucideIcons.logIn),
          SizedBox(height: AppSizes.paddingM),
          TextButton(
            onPressed: () => _tabController.animateTo(2),
            child: const Text('Забыли пароль?'),
          ),
        ],
      ),
    );
  }

  Widget _buildRegisterForm() {
    return Form(
      key: _formKeys[1],
      child: Column(
        children: [
          _buildEmailField(),
          SizedBox(height: AppSizes.paddingM),
          _buildPasswordField(
            controller: _passwordController,
            obscure: _obscurePassword,
            onToggle:
                () => setState(() => _obscurePassword = !_obscurePassword),
            showGenerator: false,
            minLength: 6,
          ),
          SizedBox(height: AppSizes.paddingM),
          _buildPasswordField(
            controller: _confirmPasswordController,
            obscure: _obscureConfirmPassword,
            onToggle:
                () => setState(
                  () => _obscureConfirmPassword = !_obscureConfirmPassword,
                ),
            labelText: 'Повторите пароль',
            isConfirmPassword: true,
          ),
          SizedBox(height: AppSizes.paddingM),
          _buildPhoneField(),
          SizedBox(height: AppSizes.paddingL),
          _buildActionButton(
            'Зарегистрироваться',
            _handleRegister,
            LucideIcons.userPlus,
          ),
        ],
      ),
    );
  }

  Widget _buildForgotPasswordForm() {
    return Form(
      key: _formKeys[2],
      child: Column(
        children: [
          Text(
            'Введите email для сброса пароля',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(
                context,
              ).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: AppSizes.paddingL),
          TextFormField(
            controller: _forgotEmailController,
            decoration: InputDecoration(
              labelText: 'Email',
              prefixIcon: const Icon(LucideIcons.mail),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            keyboardType: TextInputType.emailAddress,
            autofillHints: const [AutofillHints.email],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Введите email';
              }
              if (!RegExp(
                r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
              ).hasMatch(value)) {
                return 'Введите корректный email';
              }
              return null;
            },
          ),
          SizedBox(height: AppSizes.paddingL),
          _buildActionButton(
            'Отправить ссылку',
            _handleForgotPassword,
            LucideIcons.send,
          ),
          SizedBox(height: AppSizes.paddingM),
          TextButton(
            onPressed: () => _tabController.animateTo(0),
            child: const Text('Вернуться к входу'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      decoration: InputDecoration(
        labelText: 'Email',
        prefixIcon: const Icon(LucideIcons.mail),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: TextInputType.emailAddress,
      autofillHints: const [AutofillHints.email],
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Введите email';
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return 'Введите корректный email';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField({
    required TextEditingController controller,
    required bool obscure,
    required VoidCallback onToggle,
    String labelText = 'Пароль',
    bool showGenerator = false,
    bool isConfirmPassword = false,
    int minLength = 1,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscure,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: const Icon(LucideIcons.lock),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showGenerator)
              IconButton(
                icon: const Icon(LucideIcons.dices),
                onPressed: _useGeneratedPassword,
                tooltip: 'Сгенерировать пароль',
              ),
            IconButton(
              icon: Icon(obscure ? LucideIcons.eye : LucideIcons.eyeOff),
              onPressed: onToggle,
            ),
          ],
        ),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      autofillHints: const [AutofillHints.password],
      validator:
          isConfirmPassword
              ? (value) {
                if (value == null || value.isEmpty) {
                  return 'Повторите пароль';
                }
                if (value != _passwordController.text) {
                  return 'Пароли не совпадают';
                }
                return null;
              }
              : (value) {
                if (value == null || value.isEmpty) {
                  return 'Введите пароль';
                }
                if (value.length < minLength) {
                  return 'Пароль должен содержать минимум $minLength символов';
                }
                return null;
              },
    );
  }

  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      decoration: InputDecoration(
        labelText: 'Телефон (необязательно)',
        prefixIcon: const Icon(LucideIcons.phone),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
      ),
      keyboardType: TextInputType.phone,
      autofillHints: const [AutofillHints.telephoneNumber],
    );
  }

  Widget _buildActionButton(
    String text,
    VoidCallback onPressed,
    IconData icon,
  ) {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed: _isLoading ? null : onPressed,
        icon:
            _isLoading
                ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                )
                : Icon(icon),
        label: Text(
          text,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
