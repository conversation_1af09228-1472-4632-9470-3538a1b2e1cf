import 'dart:io';

import 'package:flutter/material.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

import 'message_item.dart';

class ChatLayout extends StatefulWidget {
  final List<AIMessage> messages;
  final ScrollController scrollController;
  final Widget Function() inputBuilder;
  final bool isMessageSending;
  final AIMessage? streamingMessage;
  final bool showScrollToBottomButton;
  final VoidCallback? onScrollToBottom;

  const ChatLayout({
    required this.messages,
    required this.scrollController,
    required this.inputBuilder,
    this.isMessageSending = false,
    this.streamingMessage,
    this.showScrollToBottomButton = false,
    this.onScrollToBottom,
    super.key,
  });

  @override
  State<ChatLayout> createState() => _ChatLayoutState();
}

class _ChatLayoutState extends State<ChatLayout> {
  bool _isNearBottom = true;
  final bool _shouldAutoScroll = true;
  int _lastMessageCount = 0;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    if (!mounted) return;

    final maxScroll = widget.scrollController.position.maxScrollExtent;
    final currentScroll = widget.scrollController.position.pixels;
    const threshold = 100.0; // px от конца

    final isNearBottom = maxScroll - currentScroll <= threshold;

    if (_isNearBottom != isNearBottom) {
      setState(() {
        _isNearBottom = isNearBottom;
      });
    }
  }

  @override
  void didUpdateWidget(ChatLayout oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Автоскролл при новых сообщениях
    final currentMessageCount =
        widget.messages.length + (widget.streamingMessage != null ? 1 : 0);

    if (currentMessageCount > _lastMessageCount) {
      _lastMessageCount = currentMessageCount;

      // Скроллим вниз если пользователь внизу или это новое сообщение
      if (_isNearBottom || _shouldAutoScroll) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && widget.scrollController.hasClients) {
            widget.scrollController.animateTo(
              widget.scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Создаем полный список сообщений включая потоковое
    final allMessages = <AIMessage>[
      ...widget.messages,
      if (widget.streamingMessage != null) widget.streamingMessage!,
    ];

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              // Основной контент
              _buildMessagesList(allMessages),

              // Кнопка прокрутки вниз
              if (!_isNearBottom && allMessages.isNotEmpty)
                _buildScrollToBottomButton(),
            ],
          ),
        ),

        // Индикатор состояния
        _buildStatusIndicator(),

        // Поле ввода
        widget.inputBuilder(),
      ],
    );
  }

  /// Строит список сообщений
  Widget _buildMessagesList(List<AIMessage> allMessages) {
    if (allMessages.isEmpty && !widget.isMessageSending) {
      return const Center(
        child: Text(
          'Начните беседу с ИИ-ассистентом',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return ListView.builder(
      controller: widget.scrollController,
      // Используем нативную физику для каждой платформы
      physics:
          Platform.isIOS
              ? const BouncingScrollPhysics()
              : const ClampingScrollPhysics(),
      padding: EdgeInsets.only(
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
        left: 8,
        right: 8,
      ),
      itemCount: allMessages.length,
      itemBuilder: (context, index) {
        final message = allMessages[index];
        final isStreaming =
            widget.streamingMessage != null &&
            message.id == widget.streamingMessage!.id;

        return MessageItem(
          key: ValueKey('${message.id}_${message.role}'),
          message: message,
          isStreaming: isStreaming,
        );
      },
    );
  }

  /// Строит кнопку прокрутки вниз
  Widget _buildScrollToBottomButton() {
    return Positioned(
      bottom: 80, // Над полем ввода
      right: 16,
      child: AnimatedOpacity(
        opacity: _isNearBottom ? 0.0 : 1.0,
        duration: const Duration(milliseconds: 200),
        child: FloatingActionButton.small(
          onPressed: () {
            widget.scrollController.animateTo(
              widget.scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeOut,
            );
          },
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          child: const Icon(Icons.keyboard_arrow_down, size: 20),
        ),
      ),
    );
  }

  /// Строит индикатор состояния
  Widget _buildStatusIndicator() {
    // Показываем индикатор только если есть активность
    if (!widget.isMessageSending && widget.streamingMessage == null) {
      return const SizedBox.shrink();
    }

    String statusText;
    if (widget.isMessageSending) {
      statusText = 'Отправляем сообщение...';
    } else if (widget.streamingMessage != null) {
      statusText = 'ИИ печатает...';
    } else {
      return const SizedBox.shrink();
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
