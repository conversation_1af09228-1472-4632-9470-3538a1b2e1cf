import 'dart:async';
import 'dart:io';

import 'package:desktop_drop/desktop_drop.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart' show kDebugMode, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/shared/constants/api_constants.dart';
import 'package:luxury_app/shared/widgets/fullscreen_image_screen.dart';

import '../data/ai_attachment.dart';
import 'widgets/audio_recorder_widget.dart';

/// Состояние загрузки файла
enum AttachmentUploadState { uploading, completed, error }

/// Расширенная модель вложения с состоянием
class AttachmentWithState {
  final AIAttachmentCreate attachment;
  final AttachmentUploadState state;
  final String? errorMessage;

  const AttachmentWithState({
    required this.attachment,
    this.state = AttachmentUploadState.completed,
    this.errorMessage,
  });

  AttachmentWithState copyWith({
    AIAttachmentCreate? attachment,
    AttachmentUploadState? state,
    String? errorMessage,
  }) {
    return AttachmentWithState(
      attachment: attachment ?? this.attachment,
      state: state ?? this.state,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

class MessageInputWidget extends ConsumerStatefulWidget {
  final TextEditingController controller;
  final void Function(String text, List<AIAttachmentCreate> attachments)
      onSubmitted;
  const MessageInputWidget({
    super.key,
    required this.controller,
    required this.onSubmitted,
  });

  @override
  ConsumerState<MessageInputWidget> createState() => _MessageInputWidgetState();
}

class _MessageInputWidgetState extends ConsumerState<MessageInputWidget>
    with TickerProviderStateMixin, LoggerMixin {
  final _imagePicker = ImagePicker();

  String? _lastTranscriptionError;

  // Список вложений с состояниями для отправки
  final List<AttachmentWithState> _attachmentsWithState = [];

  // Состояние drag & drop
  bool _isDragOver = false;

  // Состояние записи аудио
  bool _isRecording = false;

  // Таймер для автоматического скрытия ошибок
  Timer? _errorTimer;

  // Геттер для проверки наличия загружающихся файлов
  bool get _hasUploadingFiles => _attachmentsWithState.any(
        (item) => item.state == AttachmentUploadState.uploading,
      );

  // Геттер для получения списка готовых вложений
  List<AIAttachmentCreate> get _completedAttachments {
    final completed = _attachmentsWithState
        .where((item) => item.state == AttachmentUploadState.completed)
        .map((item) => item.attachment)
        .toList();

    if (kDebugMode && completed.isNotEmpty) {
      debugPrint(
        '📤 [MessageInput] _completedAttachments: ${completed.length} вложений',
      );
      for (int i = 0; i < completed.length; i++) {
        final attachment = completed[i];
        debugPrint(
          '📤 [MessageInput] Вложение $i: ${attachment.filename}, localBytes: ${attachment.localBytes != null ? "есть (${attachment.localBytes!.length} байт)" : "НЕТ"}',
        );
      }
    }

    return completed;
  }

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _errorTimer?.cancel();
    super.dispose();
  }

  void _onTextChanged() {
    if (mounted) {
      setState(() {
        // Обновляем состояние для кнопки отправки
        // Проверяем, изменилось ли состояние кнопки
      });
    }
  }

  void _startRecording() {
    // Сразу переключаем интерфейс без задержки
    setState(() {
      _isRecording = true;
      _lastTranscriptionError = null;
    });
  }

  void _onTranscriptionComplete(String text) {
    setState(() {
      _isRecording = false;
    });

    if (text.trim().isNotEmpty) {
      final current = widget.controller.text.trim();
      final newText = current.isEmpty ? text : '$current $text';
      widget.controller.text = newText;
    }
  }

  void _onRecordingError(String message) {
    setState(() {
      _isRecording = false;
      _lastTranscriptionError = message;
    });
  }

  void _onRecordingCancel() {
    setState(() {
      _isRecording = false;
    });
  }

  // Методы для работы с файлами
  Future<void> _pickImage() async {
    try {
      // На macOS лучше использовать FilePicker для изображений
      if (Platform.isMacOS) {
        final result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
        );
        if (result != null && result.files.isNotEmpty) {
          await _uploadPlatformFile(result.files.first, 'image');
        }
      } else {
        final XFile? image = await _imagePicker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 80,
        );
        if (image != null) {
          await _uploadXFile(image, 'image');
        }
      }
    } catch (e) {
      _showError('Ошибка выбора изображения: $e');
    }
  }

  Future<void> _pickCamera() async {
    try {
      // Камера не поддерживается на macOS, показываем уведомление
      if (Platform.isMacOS) {
        _showError('Камера не поддерживается на macOS. Используйте галерею.');
        return;
      }

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 80,
      );
      if (image != null) {
        await _uploadXFile(image, 'image');
      }
    } catch (e) {
      _showError('Ошибка съёмки: $e');
    }
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.any,
        allowedExtensions: null,
        dialogTitle: 'Выберите файл',
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.single;
        String type = _determineFileType(file.extension ?? '');
        await _uploadPlatformFile(file, type);
      }
    } catch (e) {
      _showError('Ошибка выбора файла: $e');
    }
  }

  // Метод для определения типа файла
  String _determineFileType(String extension) {
    final ext = extension.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'tiff'].contains(ext)) {
      return 'image';
    } else if (['mp4', 'avi', 'mov', 'mkv', 'webm', 'flv'].contains(ext)) {
      return 'video';
    } else if (['mp3', 'wav', 'aac', 'flac', 'm4a'].contains(ext)) {
      return 'audio';
    } else if (['pdf', 'doc', 'docx', 'txt', 'rtf'].contains(ext)) {
      return 'document';
    } else {
      return 'file';
    }
  }

  Future<void> _uploadFile(
    String filename,
    Uint8List finalBytes,
    String type, {
    Uint8List? originalBytes, // Добавляем параметр для оригинальных байт
  }) async {
    if (kDebugMode) {
      debugPrint(
        '🔄 [MessageInput] Начинаем загрузку файла $filename, размер: ${finalBytes.length} байт',
      );
      if (originalBytes != null) {
        debugPrint(
          '🖼️ [MessageInput] Оригинальные байты для превью: ${originalBytes.length} байт',
        );
      }
    }

    // Создаем временное вложение для превью с оригинальными байтами
    final tempAttachment = AIAttachmentCreate(
      type: type,
      url: '',
      filename: filename,
      size: finalBytes.length,
      localBytes:
          originalBytes ??
              finalBytes, // Используем оригинальные байты для превью
    );

    if (kDebugMode) {
      debugPrint(
        '📋 [MessageInput] Создано временное вложение: localBytes ${tempAttachment.localBytes != null ? "есть (${tempAttachment.localBytes!.length} байт)" : "НЕТ"}',
      );
    }

    // Создаем состояние с загрузкой
    final attachmentWithState = AttachmentWithState(
      attachment: tempAttachment,
      state: AttachmentUploadState.uploading,
    );

    setState(() {
      _attachmentsWithState.add(attachmentWithState);
    });

    final attachmentIndex = _attachmentsWithState.length - 1;

    try {
      logInfo(
        'Uploading file: $filename (${finalBytes.length} bytes, type: $type)',
      );
      if (kDebugMode) {
        debugPrint('Uploading file: $filename (${finalBytes.length} bytes)');
      }

      AIAttachmentCreate uploadedAttachment;

      // Используем соответствующий сервис в зависимости от конфигурации
      uploadedAttachment = await ref
          .read(supabaseStorageServiceProvider)
          .uploadFile(filename, finalBytes, type);

      if (kDebugMode) {
        debugPrint(
          '☁️ [MessageInput] Файл загружен на сервер: ${uploadedAttachment.url}',
        );
        debugPrint(
          '☁️ [MessageInput] Сервер вернул localBytes: ${uploadedAttachment.localBytes != null ? "есть (${uploadedAttachment.localBytes!.length} байт)" : "НЕТ"}',
        );
      }

      logInfo('File uploaded to Supabase Storage: ${uploadedAttachment.url}');

      // Обновляем состояние с успешной загрузкой
      setState(() {
        if (attachmentIndex < _attachmentsWithState.length) {
          // Сохраняем оригинальные localBytes из текущего attachment
          final currentAttachment =
              _attachmentsWithState[attachmentIndex].attachment;
          final updatedAttachment = uploadedAttachment.copyWith(
            localBytes:
                currentAttachment.localBytes, // Оригинальные байты для превью
          );

          if (kDebugMode) {
            debugPrint(
              '💾 [MessageInput] Обновляем состояние с URL: ${uploadedAttachment.url}',
            );
            debugPrint(
              '💾 [MessageInput] Сохраняем оригинальные localBytes: ${currentAttachment.localBytes != null ? "есть (${currentAttachment.localBytes!.length} байт)" : "НЕТ"}',
            );
          }

          _attachmentsWithState[attachmentIndex] =
              _attachmentsWithState[attachmentIndex].copyWith(
            attachment: updatedAttachment,
            state: AttachmentUploadState.completed,
          );

          if (kDebugMode) {
            debugPrint(
              '✅ [MessageInput] Состояние обновлено, финальный attachment localBytes: ${_attachmentsWithState[attachmentIndex].attachment.localBytes != null ? "есть (${_attachmentsWithState[attachmentIndex].attachment.localBytes!.length} байт)" : "НЕТ"}',
            );
          }
        }
      });
    } on DioException catch (e) {
      String errorMessage = 'Ошибка загрузки файла';
      if (e.response?.statusCode == 413) {
        errorMessage =
            'Файл слишком большой. Попробуйте выбрать изображение меньшего размера.';
      } else if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        errorMessage =
            'Превышено время ожидания. Проверьте подключение к интернету.';
      } else {
        errorMessage = 'Ошибка загрузки файла: ${e.message}';
      }

      logError('Error uploading file: $filename', e);

      // Обновляем состояние с ошибкой
      setState(() {
        if (attachmentIndex < _attachmentsWithState.length) {
          _attachmentsWithState[attachmentIndex] =
              _attachmentsWithState[attachmentIndex].copyWith(
            state: AttachmentUploadState.error,
            errorMessage: errorMessage,
          );
        }
      });
    } catch (e) {
      logError('Error uploading file: $filename', e);
      setState(() {
        if (attachmentIndex < _attachmentsWithState.length) {
          _attachmentsWithState[attachmentIndex] =
              _attachmentsWithState[attachmentIndex].copyWith(
            state: AttachmentUploadState.error,
            errorMessage: 'Неизвестная ошибка загрузки файла',
          );
        }
      });
    }
  }

  Future<void> _uploadXFile(XFile file, String type) async {
    final originalBytes = await file.readAsBytes();

    if (kDebugMode) {
      debugPrint(
        '📁 [MessageInput] Читаем XFile: ${file.name}, оригинальный размер: ${originalBytes.length} байт',
      );
    }

    try {
      final fileProcessingService = ref.read(fileProcessingServiceProvider);
      final processedFile = await fileProcessingService.processFile(
        file.name,
        originalBytes,
        type,
      );

      if (kDebugMode) {
        debugPrint(
          '⚙️ [MessageInput] Файл обработан: ${processedFile.filename}, обработанный размер: ${processedFile.bytes.length} байт',
        );
      }

      await _uploadFile(
        processedFile.filename,
        processedFile.bytes,
        type,
        originalBytes:
            type == 'image'
                ? originalBytes
                : null, // Сохраняем оригинал для изображений
      );
    } catch (e) {
      _showError('Ошибка обработки файла: $e');
    }
  }

  Future<void> _uploadPlatformFile(PlatformFile file, String type) async {
    Uint8List? bytes;
    String filename = file.name;

    if (kIsWeb) {
      bytes = file.bytes;
    } else {
      if (file.path != null) {
        final fileObj = File(file.path!);
        bytes = await fileObj.readAsBytes();
      }
    }

    if (bytes == null) {
      _showError('Не удалось прочитать файл');
      return;
    }

    final originalBytes = bytes;

    if (kDebugMode) {
      debugPrint(
        '📁 [MessageInput] Читаем PlatformFile: $filename, оригинальный размер: ${originalBytes.length} байт',
      );
    }

    try {
      final fileProcessingService = ref.read(fileProcessingServiceProvider);
      final processedFile = await fileProcessingService.processFile(
        filename,
        originalBytes,
        type,
      );

      if (kDebugMode) {
        debugPrint(
          '⚙️ [MessageInput] Файл обработан: ${processedFile.filename}, обработанный размер: ${processedFile.bytes.length} байт',
        );
      }

      await _uploadFile(
        processedFile.filename,
        processedFile.bytes,
        type,
        originalBytes:
            type == 'image'
                ? originalBytes
                : null, // Сохраняем оригинал для изображений
      );
    } catch (e) {
      _showError('Ошибка обработки файла: $e');
    }
  }

  void _removeAttachment(int index) {
    if (index < 0 || index >= _attachmentsWithState.length) return;

    setState(() {
      _attachmentsWithState.removeAt(index);
    });
  }

  void _retryUpload(int index) {
    if (index < 0 || index >= _attachmentsWithState.length) return;

    final attachmentWithState = _attachmentsWithState[index];
    if (attachmentWithState.state == AttachmentUploadState.error &&
        attachmentWithState.attachment.localBytes != null) {
      // Повторная попытка загрузки
      _uploadFile(
        attachmentWithState.attachment.filename ?? 'file',
        attachmentWithState.attachment.localBytes!,
        attachmentWithState.attachment.type,
      );
    }
  }

  void _showError(String message) {
    setState(() {
      _lastTranscriptionError = message;
    });
    
    // Автоматически скрываем ошибку через 8 секунд
    _errorTimer?.cancel();
    _errorTimer = Timer(const Duration(seconds: 8), () {
      if (mounted) {
        setState(() {
          _lastTranscriptionError = null;
        });
      }
    });
  }

  // Методы для drag & drop
  Future<void> _handleDroppedFiles(List<XFile> files) async {
    for (final file in files) {
      if (file.path.isNotEmpty) {
        final extension = file.path.split('.').last.toLowerCase();
        final type = _determineFileType(extension);
        await _uploadXFile(file, type);
      }
    }
  }

  void _onDragEntered() {
    setState(() {
      _isDragOver = true;
    });
  }

  void _onDragExited() {
    setState(() {
      _isDragOver = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    // Проверяем поддержку drag & drop для текущей платформы
    final supportsDropTarget =
        kIsWeb ||
            (!kIsWeb &&
                (Platform.isWindows || Platform.isMacOS || Platform.isLinux));

    final contentWidget = Container(
      padding: const EdgeInsets.all(16),
      decoration: _isDragOver && supportsDropTarget
          ? BoxDecoration(
              color: colorScheme.primaryContainer.withValues(alpha: 0.1),
              border: Border.all(
                color: colorScheme.primary,
                width: 2,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(8),
            )
          : BoxDecoration(color: colorScheme.surface),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Индикатор drag & drop (только для поддерживаемых платформ)
          if (_isDragOver && supportsDropTarget)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.primary,
                  style: BorderStyle.solid,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.file_upload, color: colorScheme.primary, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    'Отпустите файлы для загрузки',
                    style: TextStyle(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          // Список вложений
          if (_attachmentsWithState.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _attachmentsWithState.length,
                itemBuilder: (context, index) {
                  final attachmentWithState = _attachmentsWithState[index];
                  return Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: _buildAttachmentPreview(attachmentWithState, index),
                  );
                },
              ),
            ),

          // Ошибка
          if (_lastTranscriptionError != null)
            Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: colorScheme.onErrorContainer,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _lastTranscriptionError!,
                      style: TextStyle(
                        color: colorScheme.onErrorContainer,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  GestureDetector(
                    onTap: () => setState(() => _lastTranscriptionError = null),
                    child: Icon(
                      Icons.close,
                      color: colorScheme.onErrorContainer,
                      size: 16,
                    ),
                  ),
                ],
              ),
            ),

          // Основной инпут с плавным переходом
          AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            transitionBuilder: (Widget child, Animation<double> animation) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(0.0, 0.3),
                  end: Offset.zero,
                ).animate(
                  CurvedAnimation(
                    parent: animation,
                    curve: Curves.easeOutCubic,
                  ),
                ),
                child: FadeTransition(opacity: animation, child: child),
              );
            },
            child: _isRecording
                ? AudioRecorderWidget(
                    key: const ValueKey('audio_recorder'),
                    onTranscriptionComplete: _onTranscriptionComplete,
                    onError: _onRecordingError,
                    onCancel: _onRecordingCancel,
                  )
                : _buildNormalInterface(colorScheme),
          ),
        ],
      ),
    );

    return SafeArea(
      child: supportsDropTarget
          ? DropTarget(
              onDragDone: (detail) async {
                await _handleDroppedFiles(detail.files);
              },
              onDragEntered: (detail) {
                _onDragEntered();
              },
              onDragExited: (detail) {
                _onDragExited();
              },
              child: contentWidget,
            )
          : contentWidget,
    );
  }

  Widget _buildNormalInterface(ColorScheme colorScheme) {
    final hasContent =
        widget.controller.text.trim().isNotEmpty ||
            _completedAttachments.isNotEmpty;
        _completedAttachments.isNotEmpty;
    
    // Кнопка отправки активна только если есть контент и нет загружающихся файлов
    final canSend = hasContent && !_hasUploadingFiles;

    return Container(
      key: const ValueKey('normal_interface'),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Текстовое поле (сверху, на всю ширину)
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
            child: CallbackShortcuts(
              bindings: {
                const SingleActivator(LogicalKeyboardKey.enter): () {
                  if (canSend) {
                    if (kDebugMode) {
                      debugPrint(
                        '🚀 [MessageInput] Enter pressed - отправляем сообщение с ${_completedAttachments.length} вложениями',
                      );
                    }
                    widget.onSubmitted(
                      widget.controller.text,
                      _completedAttachments,
                    );
                    widget.controller.clear();
                    setState(() => _attachmentsWithState.clear());
                  }
                },
                const SingleActivator(
                  LogicalKeyboardKey.enter,
                  shift: true,
                ): () {
                  // Shift+Enter для переноса строки - не делаем ничего, поведение по умолчанию
                },
              },
              child: Focus(
                child: TextField(
                  controller: widget.controller,
                  minLines: 1,
                  maxLines: 8,
                  style: const TextStyle(fontSize: 16),
                  textInputAction: TextInputAction.newline,
                  keyboardType: TextInputType.multiline,
                  decoration: InputDecoration(
                    hintText: _hasUploadingFiles
                        ? 'Загрузка...'
                        : 'Введите сообщение...',
                    hintStyle: TextStyle(
                      color: colorScheme.onSurface.withValues(alpha: 0.5),
                      fontSize: 16,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                  onSubmitted: (text) {
                    if (canSend) {
                      if (kDebugMode) {
                        debugPrint(
                          '🚀 [MessageInput] TextField submitted - отправляем сообщение с ${_completedAttachments.length} вложениями',
                        );
                      }
                      widget.onSubmitted(text, _completedAttachments);
                      widget.controller.clear();
                      setState(() => _attachmentsWithState.clear());
                    }
                  },
                ),
              ),
            ),
          ),

          // Разделительная линия (если есть текст)
          if (widget.controller.text.trim().isNotEmpty)
            Container(
              height: 1,
              margin: const EdgeInsets.symmetric(horizontal: 12),
              color: colorScheme.onSurface.withValues(alpha: 0.1),
            ),

          // Кнопки (снизу)
          Padding(
            padding: const EdgeInsets.fromLTRB(8, 4, 8, 8),
            child: Row(
              children: [
                // Кнопка вложений
                IconButton(
                  onPressed: _hasUploadingFiles
                      ? null
                      : () => _showAttachmentOptions(colorScheme),
                  icon: Icon(
                    Icons.attach_file,
                    color: _hasUploadingFiles
                        ? colorScheme.onSurface.withValues(alpha: 0.3)
                        : colorScheme.onSurface.withValues(alpha: 0.6),
                    size: 22,
                  ),
                  padding: const EdgeInsets.all(8),
                  tooltip: 'Прикрепить файл',
                ),

                // Пружина (разделяет кнопки)
                const Spacer(),

                // Кнопка микрофона
                IconButton(
                  onPressed: _startRecording,
                  icon: Icon(
                    Icons.mic,
                    color: colorScheme.onSurface.withValues(alpha: 0.6),
                    size: 22,
                  ),
                  padding: const EdgeInsets.all(8),
                  tooltip: 'Записать голосовое сообщение',
                ),

                const SizedBox(width: 8),

                // Кнопка отправки (только когда есть контент)
                if (hasContent)
                  IconButton(
                    onPressed: canSend ? () {
                      if (kDebugMode) {
                        debugPrint(
                          '🚀 [MessageInput] Send button pressed - отправляем сообщение с ${_completedAttachments.length} вложениями',
                        );
                      }
                      widget.onSubmitted(
                        widget.controller.text,
                        _completedAttachments,
                      );
                      widget.controller.clear();
                      setState(() => _attachmentsWithState.clear());
                    } : null,
                    icon: Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        color: canSend ? colorScheme.primary : colorScheme.onSurface.withAlpha(64),
                        shape: BoxShape.circle,
                      ),
                      child: _hasUploadingFiles
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: colorScheme.onSurface.withAlpha(128),
                              ),
                            )
                          : Icon(
                              Icons.arrow_upward,
                              color: canSend ? colorScheme.onPrimary : colorScheme.onSurface.withAlpha(128),
                              size: 20,
                            ),
                    ),
                    padding: const EdgeInsets.all(4),
                    tooltip: _hasUploadingFiles ? 'Загрузка файлов...' : 'Отправить сообщение',
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showAttachmentOptions(ColorScheme colorScheme) {
    showModalBottomSheet(
      context: context,
      backgroundColor: colorScheme.surface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(
                Icons.photo_library,
                color: colorScheme.primary,
              ),
              title: const Text('Галерея'),
              onTap: () {
                Navigator.pop(context);
                _pickImage();
              },
            ),
            if (!Platform.isMacOS)
              ListTile(
                leading: Icon(Icons.camera_alt, color: colorScheme.primary),
                title: const Text('Камера'),
                onTap: () {
                  Navigator.pop(context);
                  _pickCamera();
                },
              ),
            ListTile(
              leading: Icon(Icons.attach_file, color: colorScheme.primary),
              title: const Text('Файл'),
              onTap: () {
                Navigator.pop(context);
                _pickFile();
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getAttachmentIcon(String type) {
    switch (type) {
      case 'image':
        return Icons.image;
      case 'video':
        return Icons.videocam;
      case 'audio':
        return Icons.audiotrack;
      default:
        return Icons.insert_drive_file;
    }
  }

  void _openFullscreenImagePreview(AttachmentWithState attachmentWithState) {
    final attachment = attachmentWithState.attachment;
    String? imageUrl;
    Uint8List? imageBytes;

    if (attachment.localBytes != null) {
      imageBytes = attachment.localBytes;
    } else if (attachment.url.isNotEmpty) {
      if (attachment.url.startsWith('http')) {
        imageUrl = attachment.url;
      } else {
        imageUrl = '${ApiConstants.baseImageUrl}${attachment.url}';
      }
    }

    if (imageUrl != null || imageBytes != null) {
      String heroTag = imageUrl ?? 'preview_image_${attachment.hashCode}';

      Navigator.of(context).push(
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) => FullscreenImageScreen(
            imageUrl: imageUrl,
            imageBytes: imageBytes,
            heroTag: heroTag,
          ),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 300),
          reverseTransitionDuration: const Duration(milliseconds: 300),
          opaque: false,
        ),
      );
    }
  }

  Widget _buildAttachmentPreview(
    AttachmentWithState attachmentWithState,
    int index,
  ) {
    final attachment = attachmentWithState.attachment;
    final colorScheme = Theme.of(context).colorScheme;

    // Общий контейнер для превью с кнопками управления
    return Stack(
      alignment: Alignment.topRight,
      children: [
        // Основное тело превью
        GestureDetector(
          onTap: attachment.type == 'image' &&
                  attachmentWithState.state != AttachmentUploadState.error
              ? () => _openFullscreenImagePreview(attachmentWithState)
              : null,
          child: Container(
            width: 80,
            height: 80,
            margin: const EdgeInsets.only(
              right: 8,
              top: 8,
            ), // Отступ для кнопок управления
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: attachmentWithState.state == AttachmentUploadState.error
                    ? colorScheme.error
                    : colorScheme.outline.withAlpha(128),
              ),
              color: colorScheme.surfaceContainer,
            ),
            child: ClipRRect(
              // Обрезаем содержимое по рамке
              borderRadius: BorderRadius.circular(
                7,
              ), // Чуть меньше, чем у контейнера
              child: _buildPreviewContent(attachmentWithState, colorScheme),
            ),
          ),
        ),
        // Кнопки управления
        Positioned(
          top: 0,
          right: 0,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Кнопка повтора загрузки (только при ошибке)
              if (attachmentWithState.state == AttachmentUploadState.error)
                InkWell(
                  onTap: () => _retryUpload(index),
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    margin: const EdgeInsets.only(right: 2),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withAlpha(204),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.refresh,
                      size: 14,
                      color: colorScheme.onPrimary,
                    ),
                  ),
                ),
              // Кнопка удаления
              InkWell(
                onTap: () => _removeAttachment(index),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: colorScheme.errorContainer.withAlpha(204),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.close,
                    size: 14,
                    color: colorScheme.onErrorContainer,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Вспомогательный виджет для содержимого превью
  Widget _buildPreviewContent(
    AttachmentWithState attachmentWithState,
    ColorScheme colorScheme,
  ) {
    final attachment = attachmentWithState.attachment;
    final isUploading =
        attachmentWithState.state == AttachmentUploadState.uploading;
    final hasError = attachmentWithState.state == AttachmentUploadState.error;

    if (attachment.type == 'image') {
      if (attachment.localBytes != null) {
        // Есть локальные байты - показываем их
        return Stack(
          fit: StackFit.expand,
          alignment: Alignment.center,
          children: [
            Image.memory(
              attachment.localBytes!,
              fit: BoxFit.cover,
              gaplessPlayback: true, // Поддержка анимированных GIF
              errorBuilder: (context, error, stackTrace) {
                // Ошибка отображения Image.memory (маловероятно, но на всякий случай)
                return Center(
                  child: Icon(
                    Icons.broken_image,
                    color: colorScheme.onSurfaceVariant,
                    size: 32,
                  ),
                );
              },
            ),
            if (isUploading)
              Container(
                color: Colors.black.withAlpha(77),
                child: const Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            if (hasError)
              Container(
                color: Colors.black.withAlpha(77),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: colorScheme.error,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Ошибка',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      } else if (attachment.url.isNotEmpty) {
        // Нет локальных байт, но есть URL (предполагаем, что это серверный)
        String imageUrl = attachment.url;
        if (!imageUrl.startsWith('http')) {
          imageUrl = Uri.parse(
            ApiConstants.baseImageUrl,
          ).replace(path: attachment.url).toString();
        }
        return Image.network(
          imageUrl,
          fit: BoxFit.cover,
          gaplessPlayback: true, // Поддержка анимированных GIF
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Center(
              child: Icon(
                Icons.error_outline,
                color: colorScheme.error,
                size: 32,
              ),
            );
          },
        );
      } else {
        // Нет ни локальных байт, ни URL - состояние ошибки
        return Center(
          child: Icon(
            Icons.image_not_supported_outlined,
            color: colorScheme.onSurfaceVariant,
            size: 32,
          ),
        );
      }
    } else {
      // Для не-изображений (файлы, аудио и т.д.)
      return Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getAttachmentIcon(attachment.type),
            size: 32,
            color: hasError ? colorScheme.error : colorScheme.primary,
          ),
          const SizedBox(height: 4),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Text(
              attachment.filename ?? 'файл',
              style: TextStyle(
                fontSize: 10,
                color:
                    hasError ? colorScheme.error : colorScheme.onSurfaceVariant,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 4),
          if (isUploading)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 1.5),
            )
          else if (hasError)
            Tooltip(
              message: attachmentWithState.errorMessage ?? 'Ошибка загрузки',
              child: Icon(
                Icons.error_outline,
                color: colorScheme.error,
                size: 16,
              ),
            ),
        ],
      );
    }
  }
}
