import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/core/services/connectivity_service.dart';
import 'package:luxury_app/features/ai_chat/chat_list/chat_list_provider.dart';
import 'package:luxury_app/features/ai_chat/data/ai_attachment.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_repository.dart';
import 'package:luxury_app/features/ai_chat/data/ai_message.dart';

/// Состояние AI чата
class AIChatState {
  final List<AIMessage> messages;
  final bool isLoading;
  final bool isRecording;
  final bool isPlayingAudio;
  final bool isMessageSending;
  final AIMessage? streamingMessage;
  final int? selectedChatId;
  final String? currentAudioFile;
  final String? error;

  const AIChatState({
    this.messages = const [],
    this.isLoading = false,
    this.isRecording = false,
    this.isPlayingAudio = false,
    this.isMessageSending = false,
    this.streamingMessage,
    this.selectedChatId,
    this.currentAudioFile,
    this.error,
  });

  /// Проверяет, есть ли ошибка
  bool get hasError => error != null;

  AIChatState copyWith({
    List<AIMessage>? messages,
    bool? isLoading,
    bool? isRecording,
    bool? isPlayingAudio,
    bool? isMessageSending,
    AIMessage? streamingMessage,
    bool clearStreamingMessage = false,
    int? selectedChatId,
    String? currentAudioFile,
    String? error,
    bool clearError = false,
  }) {
    return AIChatState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      isRecording: isRecording ?? this.isRecording,
      isPlayingAudio: isPlayingAudio ?? this.isPlayingAudio,
      isMessageSending: isMessageSending ?? this.isMessageSending,
      streamingMessage:
          clearStreamingMessage
              ? null
              : (streamingMessage ?? this.streamingMessage),
      selectedChatId: selectedChatId ?? this.selectedChatId,
      currentAudioFile: currentAudioFile ?? this.currentAudioFile,
      error: clearError ? null : (error ?? this.error),
    );
  }
}

/// Notifier для управления состоянием AI чата
class AIChatNotifier extends StateNotifier<AIChatState> {
  final AIChatRepository _chatRepository;
  final int _chatId;
  final Ref _ref;

  StreamSubscription? _streamSubscription;
  StreamSubscription<List<AIMessage>>? _messagesSubscription;

  // Простые переменные для потокового контента
  String _streamingContent = '';
  bool _isStreamActive = false;
  AIMessage? _currentStreamingMessage;

  AIChatNotifier(this._chatRepository, this._chatId, this._ref)
      : super(const AIChatState()) {
    _initializeChat();
  }

  @override
  void dispose() {
    _streamSubscription?.cancel();
    _messagesSubscription?.cancel();
    super.dispose();
  }

  /// Инициализация чата
  void _initializeChat() {
    state = state.copyWith(
      isLoading: true,
      selectedChatId: _chatId,
      clearError: true,
    );

    _subscribeToMessages();
  }

  /// Подписка на real-time обновления сообщений
  void _subscribeToMessages() {
    try {
      _messagesSubscription = _chatRepository
          .subscribeToMessages(_chatId)
          .listen(
            (messages) {
              if (kDebugMode) {
                debugPrint(
                  '✅ Получено ${messages.length} сообщений через real-time',
                );
              }

              // Простая логика обновления без сложных проверок
              if (mounted) {
                // Если есть активный стрим, проверяем появление нового сообщения AI
                if (_isStreamActive && _currentStreamingMessage != null) {
                  // Ищем новое сообщение AI, которое появилось недавно
                  final newAIMessage = messages.lastWhereOrNull(
                    (msg) =>
                        msg.role == 'assistant' &&
                        msg.content != null &&
                        msg.content!.isNotEmpty &&
                        msg.createdAt.isAfter(
                          DateTime.now().subtract(const Duration(seconds: 30)),
                        ),
                  );

                  if (newAIMessage != null) {
                    // Новое сообщение AI появилось, завершаем стрим
                    _finishStreaming();
                  }
                }

                state = state.copyWith(messages: messages, isLoading: false);
              }
            },
            onError: (error) {
              if (kDebugMode) {
                debugPrint('❌ Ошибка real-time подписки: $error');
              }
              // Не показываем ошибку пользователю - данные из кэша уже загружены
              if (mounted) {
                state = state.copyWith(isLoading: false);
              }
            },
          );
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка создания real-time подписки: $e');
      }
      // Не показываем ошибку пользователю - данные из кэша уже загружены
      if (mounted) {
        state = state.copyWith(isLoading: false);
      }
    }
  }

  /// Загружает сообщения для чата
  Future<void> loadMessages() async {
    if (!mounted) return;

    state = state.copyWith(
      isLoading: true,
      selectedChatId: _chatId,
      clearError: true,
    );

    try {
      final messages = await _chatRepository.getChatMessages(_chatId);
      if (mounted) {
        state = state.copyWith(messages: messages, isLoading: false);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка загрузки сообщений: $e');
      }
      if (mounted) {
        state = state.copyWith(isLoading: false, error: e.toString());
      }
    }
  }

  /// Отправляет сообщение
  Future<void> sendMessage({
    required String content,
    List<AIAttachment>? attachments,
  }) async {
    if (!mounted) return;

    if (content.trim().isEmpty &&
        (attachments == null || attachments.isEmpty)) {
      return;
    }

    // Проверяем подключение к сети
    if (!ConnectivityService.instance.isSupabaseConnected) {
      state = state.copyWith(error: 'Нет подключения к интернету для отправки сообщения.');
      return;
    }

    // Отменяем предыдущий поток
    await _cancelCurrentStream();

    // Проверяем, первое ли это сообщение пользователя
    final isFirstUserMessage =
        state.messages.where((msg) => msg.role == 'user').isEmpty;

    // Создаем оптимистичное сообщение пользователя
    final optimisticUserMessage = AIMessage(
      id: -DateTime.now().millisecondsSinceEpoch,
      role: 'user',
      content: content,
      chatId: _chatId,
      createdAt: DateTime.now(),
      attachments: attachments ?? [],
    );

    if (kDebugMode) {
      debugPrint('📝 Создаем оптимистичное сообщение пользователя');
      if (attachments != null && attachments.isNotEmpty) {
        for (int i = 0; i < attachments.length; i++) {
          final attachment = attachments[i];
          debugPrint(
            '📎 Вложение $i: ${attachment.filename}, localBytes: ${attachment.localBytes != null ? "есть (${attachment.localBytes!.length} байт)" : "нет"}',
          );
        }
      }
    }

    // Добавляем в состояние
    state = state.copyWith(
      messages: [...state.messages, optimisticUserMessage],
      isMessageSending: true,
      clearError: true,
      clearStreamingMessage: true,
    );

    // Обновляем название чата если нужно
    if (isFirstUserMessage) {
      _updateChatTitle(content);
    }

    try {
      // Начинаем стрим
      await _startStreaming(content, attachments);
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка отправки сообщения: $e');
      }

      if (mounted) {
        // Удаляем оптимистичное сообщение при ошибке
        final messagesWithoutOptimistic =
            state.messages
                .where((msg) => msg.id != optimisticUserMessage.id)
                .toList();

        state = state.copyWith(
          messages: messagesWithoutOptimistic,
          isMessageSending: false,
          clearStreamingMessage: true,
          error: e.toString(),
        );
      }
    }
  }

  /// Начинает потоковую передачу
  Future<void> _startStreaming(
    String content,
    List<AIAttachment>? attachments,
  ) async {
    _isStreamActive = true;
    _streamingContent = '';

    // Создаем сообщение для стрима
    _currentStreamingMessage = AIMessage(
      id: -1,
      role: 'assistant',
      content: '',
      chatId: _chatId,
      createdAt: DateTime.now(),
      attachments: [],
    );

    try {
      _streamSubscription = _chatRepository
          .sendMessageToChatStream(
            _chatId,
            AISendMessageRequest(
              content: content,
              attachments:
                  attachments
                      ?.map(
                        (a) => AIAttachmentCreate(
                          type: a.type,
                          url: a.url,
                          filename: a.filename,
                          size: a.size,
                          mimeType: a.mimeType,
                        ),
                      )
                      .toList(),
            ),
          )
          .listen(
            _handleStreamEvent,
            onError: _handleStreamError,
            onDone: _handleStreamDone,
          );
    } catch (e) {
      _handleStreamError(e);
    }
  }

  /// Обрабатывает событие стрима
  void _handleStreamEvent(String eventString) {
    if (!mounted || !_isStreamActive) return;

    try {
      final event = jsonDecode(eventString) as Map<String, dynamic>;

      if (event['type'] == 'completion') {
        final newContent = event['data']?['content'] as String?;
        if (newContent != null) {
          _streamingContent += newContent;

          // Обновляем сообщение
          if (_currentStreamingMessage != null) {
            _currentStreamingMessage = _currentStreamingMessage!.copyWith(
              content: _streamingContent,
            );

            // Простое обновление состояния
            if (state.isMessageSending) {
              // Первый чанк - убираем индикатор отправки
              state = state.copyWith(
                isMessageSending: false,
                streamingMessage: _currentStreamingMessage,
              );
            } else {
              // Последующие чанки - просто обновляем контент
              state = state.copyWith(
                streamingMessage: _currentStreamingMessage,
              );
            }
          }
        }
      } else if (event['type'] == 'done') {
        if (kDebugMode) {
          debugPrint('✅ Стрим завершен');
        }
        _finishStreaming();
      } else if (event['type'] == 'error') {
        final errorMessage =
            event['data']?['message'] as String? ?? 'Произошла ошибка';
        if (kDebugMode) {
          debugPrint('❌ Ошибка в стриме: $errorMessage');
        }
        _handleStreamError(errorMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Ошибка парсинга события: $e');
      }
      _handleStreamError(e);
    }
  }

  /// Обрабатывает ошибку стрима
  void _handleStreamError(dynamic error) {
    if (!mounted) return;

    _isStreamActive = false;
    _streamingContent = '';
    _currentStreamingMessage = null;

    if (kDebugMode) {
      debugPrint('❌ Ошибка стрима: $error');
    }

    state = state.copyWith(
      isMessageSending: false,
      clearStreamingMessage: true,
      error: error.toString(),
    );
  }

  /// Обрабатывает завершение стрима
  void _handleStreamDone() {
    if (kDebugMode) {
      debugPrint('✅ Стрим завершен через onDone');
    }
    _finishStreaming();
  }

  /// Завершает стрим
  void _finishStreaming() {
    if (!mounted) return;

    _isStreamActive = false;
    _streamingContent = '';
    _currentStreamingMessage = null;

    state = state.copyWith(
      isMessageSending: false,
      clearStreamingMessage: true,
    );
  }

  /// Отменяет текущий стрим
  Future<void> _cancelCurrentStream() async {
    _streamSubscription?.cancel();
    _streamSubscription = null;
    _isStreamActive = false;
    _streamingContent = '';
    _currentStreamingMessage = null;
  }

  /// Обновляет название чата
  void _updateChatTitle(String content) {
    _ref
        .read(chatListProvider.notifier)
        .updateChatTitleFromFirstMessage(_chatId, content)
        .catchError((e) {
          if (kDebugMode) {
            debugPrint('❌ Ошибка обновления названия чата: $e');
          }
        });
  }

  /// Сбрасывает состояние чата
  void resetChatState() {
    _cancelCurrentStream();
    state = const AIChatState();
    _initializeChat();
  }

  /// Начинает запись аудио
  void startRecording() {
    state = state.copyWith(isRecording: true);
  }

  /// Останавливает запись аудио
  void stopRecording() {
    state = state.copyWith(isRecording: false);
  }

  /// Воспроизводит аудио сообщение
  void playAudio(String audioFile) {
    state = state.copyWith(isPlayingAudio: true, currentAudioFile: audioFile);
  }

  /// Останавливает воспроизведение аудио
  void stopAudio() {
    state = state.copyWith(isPlayingAudio: false, currentAudioFile: null);
  }

  /// Очищает ошибки
  void clearError() {
    state = state.copyWith(clearError: true);
  }
}

/// Основной провайдер для AI чата с поддержкой chatId
final aiChatProvider =
    StateNotifierProvider.family<AIChatNotifier, AIChatState, int>((
      ref,
      chatId,
    ) {
      final chatRepository = ref.read(aiChatRepositoryProvider);
      return AIChatNotifier(chatRepository, chatId, ref);
    });

/// Расширение для поиска элемента с условием
extension ListExtensions<T> on List<T> {
  T? lastWhereOrNull(bool Function(T element) test) {
    for (int i = length - 1; i >= 0; i--) {
      if (test(this[i])) return this[i];
    }
    return null;
  }
}
