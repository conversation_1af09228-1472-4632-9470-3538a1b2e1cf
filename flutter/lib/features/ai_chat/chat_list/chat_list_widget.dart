import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/core/mixins/logger_mixin.dart';
import 'package:luxury_app/features/ai_chat/chat_list/chat_list_provider.dart';
import 'package:luxury_app/features/ai_chat/data/ai_chat_model.dart';
import 'package:luxury_app/features/drawer/create_chat_button.dart';
import 'package:luxury_app/features/drawer/please_sign_in.dart';
import 'package:luxury_app/features/settings/auth/auth_provider.dart';
import 'package:luxury_app/navigation/route_config.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/utils/date_formatter.dart';
import 'package:luxury_app/shared/widgets/index.dart';

/// Виджет для отображения списка чатов с использованием Riverpod
class ChatListWidget extends ConsumerStatefulWidget {
  /// Ключ scaffold для закрытия drawer на мобильных устройствах
  final GlobalKey<ScaffoldState>? scaffoldKey;
  final String searchTerm;

  const ChatListWidget({super.key, this.scaffoldKey, this.searchTerm = ''});

  @override
  ConsumerState<ChatListWidget> createState() => _ChatListWidgetState();
}

class _ChatListWidgetState extends ConsumerState<ChatListWidget>
    with AutomaticKeepAliveClientMixin, LoggerMixin {
  final _searchController = TextEditingController();
  String _searchTerm = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchTerm = _searchController.text.toLowerCase();
      });
    });
    _searchTerm = widget.searchTerm;

    // Загружаем чаты при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      logInfo('🔄 Инициализация ChatListWidget - загружаем чаты');
      ref.read(chatListProvider.notifier).loadChats();
    });
  }

  @override
  void didUpdateWidget(covariant ChatListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchTerm != oldWidget.searchTerm) {
      setState(() {
        _searchTerm = widget.searchTerm;
      });
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Для AutomaticKeepAliveClientMixin

    // Проверяем авторизацию
    final authState = ref.watch(authProvider);

    if (!authState.isAuthenticated) {
      return const PleaseSignIn();
    }

    // Слушаем изменения авторизации
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Перезагружаем чаты при смене авторизации
      if (next.isAuthenticated && (previous?.isAuthenticated != true)) {
        ref.read(chatListProvider.notifier).refreshChats();
      }
    });

    // Слушаем ошибки из провайдера
    ref.listen<ChatListState>(chatListProvider, (previous, next) {
      if (next.hasError && (previous?.error != next.error)) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.error!),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        // Очищаем ошибку после показа
        ref.read(chatListProvider.notifier).clearError();
      }
    });

    return _buildChatList();
  }

  Widget _buildChatList() {
    final chatListState = ref.watch(chatListProvider);
    final filteredChats = ref.watch(filteredChatListProvider(_searchTerm));

    logInfo(
      '🔄 _buildChatList: состояние - isLoading=${chatListState.isLoading}, isInitialized=${chatListState.isInitialized}, chats.length=${chatListState.chats.length}, error=${chatListState.error}',
    );
    logInfo('🔄 _buildChatList: filteredChats.length=${filteredChats.length}');

    return Column(
      children: [
        // Поисковая строка
        _buildSearchBar(),

        // Кнопка создания чата
        _buildCreateChatButton(),

        // Список чатов
        Expanded(child: _buildChatListContent(chatListState, filteredChats)),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(AppSizes.paddingS),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Поиск чатов...',
          prefixIcon: const Icon(LucideIcons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(AppSizes.radiusM),
          ),
        ),
      ),
    );
  }

  Widget _buildCreateChatButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: CreateChatButton(
        onCreateChat: () async {
          // Создаем новый чат
          final newChat = await ref
              .read(chatListProvider.notifier)
              .createChat('Новый чат');
          if (newChat != null && mounted) {
            // Навигация к новому чату
            context.goNamed(
              RouteConfig.aiRoute,
              pathParameters: {'chatId': newChat.id.toString()},
            );

            // Закрываем drawer на мобильных устройствах
            if (widget.scaffoldKey?.currentState?.isDrawerOpen == true) {
              widget.scaffoldKey!.currentState!.closeDrawer();
            }
          }
        },
      ),
    );
  }

  Widget _buildChatListContent(
    ChatListState chatListState,
    List<AIChat> filteredChats,
  ) {
    if (chatListState.isLoading && !chatListState.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // Ошибку теперь обрабатывает SnackBar, но оставим виджет для критических случаев
    if (chatListState.hasError && filteredChats.isEmpty) {
      return _buildErrorState(chatListState.error!);
    }

    if (filteredChats.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: AppSizes.paddingS),
      itemCount: filteredChats.length,
      itemBuilder: (context, index) {
        final chat = filteredChats[index];
        return _buildChatItem(chat);
      },
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(LucideIcons.alertCircle, size: 48, color: Colors.red),
          const SizedBox(height: 16.0),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16.0),
          AppButton(
            text: 'Повторить',
            onPressed: () => ref.read(chatListProvider.notifier).refreshChats(),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(LucideIcons.messageCircle, size: 48, color: Colors.grey),
          const SizedBox(height: 16.0),
          Text(
            _searchTerm.isEmpty ? 'Нет чатов' : 'Чаты не найдены',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          if (_searchTerm.isEmpty) ...[
            const SizedBox(height: 16.0),
            Text(
              'Создайте новый чат для начала общения',
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChatItem(AIChat chat) {
    return Consumer(
      builder: (context, ref, child) {
        final appState = ref.watch(appProvider);
        final isSelected =
            appState.activeScreen == DrawerMode.chat &&
            appState.lastRequestedAssistantId == chat.id.toString();

        // Отладочный лог для проверки состояния
        if (kDebugMode) {
          debugPrint(
            '🔍 ChatItem для чата ${chat.id}: activeScreen=${appState.activeScreen}, '
            'lastRequestedAssistantId=${appState.lastRequestedAssistantId}, '
            'isSelected=$isSelected',
          );
        }

        return DrawerListItem(
          title: chat.title,
          subtitle: DateFormatter.formatRelativeTime(chat.updatedAt),
          leading: _buildChatIcon(isSelected),
          trailing: _buildChatMenu(chat),
          isActive: isSelected,
          onTap: () {
            context.goNamed(
              RouteConfig.aiRoute,
              pathParameters: {'chatId': chat.id.toString()},
            );

            // Закрываем drawer на мобильных устройствах
            if (widget.scaffoldKey?.currentState?.isDrawerOpen == true) {
              widget.scaffoldKey!.currentState!.closeDrawer();
            }
          },
        );
      },
    );
  }

  /// Строит иконку для чата (с индикатором для активного чата)
  Widget _buildChatIcon(bool isSelected) {
    final theme = Theme.of(context);
    final iconSize = AppSizes.navIconSize * AppSizes.scaleSmall;

    if (isSelected) {
      // Для активного чата показываем точку-индикатор и иконку
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.dot,
            color: theme.colorScheme.primary,
            size: iconSize * 0.7,
          ),
          const SizedBox(width: 2),
          Icon(
            LucideIcons.messageCircle,
            size: iconSize,
            color: theme.colorScheme.primary,
          ),
        ],
      );
    } else {
      // Стандартная иконка чата
      return Icon(
        LucideIcons.messageCircle,
        size: iconSize,
        color: theme.colorScheme.primary,
      );
    }
  }

  /// Строит меню действий для чата
  Widget _buildChatMenu(AIChat chat) {
    return PopupMenuButton<String>(
      onSelected: (value) => _handleChatAction(value, chat),
      icon: Icon(
        LucideIcons.moreVertical,
        size: AppSizes.navIconSize * AppSizes.scaleSmall,
        color: Theme.of(context).colorScheme.onSurface.withAlpha(153),
      ),
      itemBuilder:
          (context) => [
            const PopupMenuItem(
              value: 'rename',
              child: Row(
                children: [
                  Icon(LucideIcons.edit, size: 16.0),
                  SizedBox(width: 8.0),
                  Text('Переименовать'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(LucideIcons.trash2, color: Colors.red, size: 16.0),
                  SizedBox(width: 8.0),
                  Text('Удалить', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
    );
  }

  void _handleChatAction(String action, AIChat chat) {
    switch (action) {
      case 'rename':
        _showRenameDialog(chat);
        break;
      case 'delete':
        _showDeleteDialog(chat);
        break;
    }
  }

  void _showRenameDialog(AIChat chat) {
    final controller = TextEditingController(text: chat.title);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Переименовать чат'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'Название чата',
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () {
                  final newTitle = controller.text.trim();
                  if (newTitle.isNotEmpty && newTitle != chat.title) {
                    ref
                        .read(chatListProvider.notifier)
                        .renameChat(chat.id, newTitle);
                  }
                  Navigator.of(context).pop();
                },
                child: const Text('Сохранить'),
              ),
            ],
          ),
    );
  }

  void _showDeleteDialog(AIChat chat) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Удалить чат'),
            content: Text(
              'Вы уверены, что хотите удалить чат "${chat.title}"?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () async {
                  final navigator = Navigator.of(context);
                  final appState = ref.read(appProvider);
                  final goRouter = GoRouter.of(context);

                  navigator.pop();
                  final shouldNavigateToNews =
                      appState.activeScreen == DrawerMode.chat &&
                      appState.lastRequestedAssistantId == chat.id.toString();

                  await ref.read(chatListProvider.notifier).deleteChat(chat.id);

                  // Если удаляем активный чат, переходим на главную
                  if (shouldNavigateToNews && mounted) {
                    goRouter.goNamed(RouteConfig.newsRoute);
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Удалить'),
              ),
            ],
          ),
    );
  }
}
