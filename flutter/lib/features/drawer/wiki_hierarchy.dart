import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart';
import 'package:luxury_app/shared/constants/sizes.dart';
import 'package:luxury_app/shared/constants/strings.dart';
import 'package:luxury_app/shared/widgets/drawer_list_item.dart';

/// Компонент отображения иерархической структуры wiki (папок и файлов) в боковой панели
/// Отвечает за отображение и навигацию по дереву файлов и папок
class WikiHierarchy extends StatefulWidget {
  final List<WikiFolder> folders;
  final bool isLoading;
  final String? error;
  final bool isSearching;
  final String? selectedFileId;
  final bool allExpanded;
  final Set<String> expandedFolders;
  final TextEditingController searchController;
  final ValueChanged<String> onSearch;
  final VoidCallback onExpandCollapse;
  final VoidCallback onClearSearch;
  final ValueChanged<WikiFile> onFileTap;
  final ValueChanged<String> onToggleFolder;

  const WikiHierarchy({
    super.key,
    required this.folders,
    required this.isLoading,
    required this.error,
    required this.isSearching,
    required this.selectedFileId,
    required this.allExpanded,
    required this.expandedFolders,
    required this.searchController,
    required this.onSearch,
    required this.onExpandCollapse,
    required this.onClearSearch,
    required this.onToggleFolder,
    required this.onFileTap,
  });

  @override
  State<WikiHierarchy> createState() => _WikiHierarchyState();
}

class _WikiHierarchyState extends State<WikiHierarchy>
    with AutomaticKeepAliveClientMixin {
  /// Фокус для поля поиска
  final FocusNode _searchFocusNode = FocusNode();

  /// Контроллер скролла для автоматического поиска элементов
  final ScrollController _scrollController = ScrollController();

  /// Map для хранения GlobalKey каждого элемента файла
  final Map<String, GlobalKey> _fileKeys = {};

  /// Последний выбранный файл для предотвращения дублирования скролла
  String? _lastSelectedFileId;

  /// Сохраняем состояние виджета при переключении вкладок
  @override
  bool get wantKeepAlive => true;

  @override
  void dispose() {
    _searchFocusNode.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// Получает или создает GlobalKey для файла (кэширование)
  GlobalKey _getKeyForFile(String fileId) {
    return _fileKeys.putIfAbsent(fileId, () => GlobalKey(debugLabel: 'wiki_file_$fileId'));
  }

  /// Автоматически скроллит к выбранному файлу (оптимизированная версия)
  Future<void> _scrollToSelectedFile() async {
    final selectedFileId = widget.selectedFileId;

    if (selectedFileId == null ||
        selectedFileId == _lastSelectedFileId ||
        !_scrollController.hasClients) {
      return;
    }

    _lastSelectedFileId = selectedFileId;

    // Используем addPostFrameCallback для более надежного скроллинга
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      
      final key = _fileKeys[selectedFileId];
      if (key?.currentContext != null) {
        try {
          await Scrollable.ensureVisible(
            key!.currentContext!,
            duration: const Duration(milliseconds: 300), // Уменьшаем время анимации
            curve: Curves.easeOut, // Более быстрая кривая
            alignment: 0.3, // Показать элемент в верхней трети экрана
          );
        } catch (e) {
          debugPrint('Ошибка автоматического скролла: $e');
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Column(
      children: [
        // Верхняя панель с поиском и управлением
        _buildSearchBar(),
        // Основной контент со списком папок и файлов
        Expanded(child: _buildContent()),
      ],
    );
  }

  /// Строит поисковую панель с кнопками управления
  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.onSurface.withAlpha(
            18,
          ), // Явное выделение для темной темы
          borderRadius: BorderRadius.circular(AppSizes.radiusM),
          border: Border.all(
            color:
                _searchFocusNode.hasFocus
                    ? Theme.of(context).colorScheme.primary.withAlpha(100)
                    : Colors.transparent,
            width: 1.5,
          ),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        height: 40,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Иконка поиска
            Icon(
              LucideIcons.search,
              size: 20,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: 8),
            // Поле ввода
            Expanded(child: _buildSearchField()),
            // Кнопка разворачивания/сворачивания всех папок
            _buildExpandCollapseButton(),
            // Кнопка очистки поиска (отображается только при наличии текста)
            if (widget.searchController.text.isNotEmpty)
              _buildClearSearchButton(),
          ],
        ),
      ),
    );
  }

  /// Строит поле ввода для поиска
  Widget _buildSearchField() {
    return TextField(
      controller: widget.searchController,
      focusNode: _searchFocusNode,
      decoration: const InputDecoration(
        hintText: 'Поиск',
        hintStyle: null, // Используем стиль по умолчанию
        border: InputBorder.none,
        enabledBorder: InputBorder.none,
        focusedBorder: InputBorder.none,
        isDense: true,
        contentPadding: EdgeInsets.symmetric(vertical: 4, horizontal: 0),
        filled: false, // fillColor не задаём вообще
      ),
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurface,
      ),
      cursorColor: Theme.of(context).colorScheme.primary,
      onChanged: widget.onSearch,
    );
  }

  /// Строит кнопку для разворачивания/сворачивания всех папок
  Widget _buildExpandCollapseButton() {
    return Tooltip(
      message: widget.allExpanded ? 'Свернуть все папки' : 'Раскрыть все папки',
      child: IconButton(
        icon: Icon(
          widget.allExpanded ? Icons.arrow_drop_up : Icons.arrow_drop_down,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        splashRadius: 18,
        onPressed: widget.onExpandCollapse,
      ),
    );
  }

  /// Строит кнопку для очистки поля поиска
  Widget _buildClearSearchButton() {
    return IconButton(
      icon: Icon(
        Icons.clear,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      onPressed: widget.onClearSearch,
    );
  }

  /// Строит основной контент в зависимости от состояния (загрузка, ошибка, результаты)
  Widget _buildContent() {
    // Добавляем отладочную информацию
    debugPrint(
      '🔍 WikiHierarchy _buildContent: isLoading=${widget.isLoading}, folders.length=${widget.folders.length}, error=${widget.error}',
    );

    // При загрузке данных показываем индикатор
    if (widget.isLoading) {
      debugPrint('🔄 WikiHierarchy: Показываем индикатор загрузки');
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Загрузка Wiki...'),
          ],
        ),
      );
    }

    // Если выполняется поиск и результаты пусты
    if (widget.isSearching && widget.folders.isEmpty) {
      debugPrint('🔍 WikiHierarchy: Показываем пустые результаты поиска');
      return _buildEmptySearchResults();
    }

    // Если есть ошибка, но есть и папки - показываем баннер ошибки и дерево
    if (widget.error != null && widget.folders.isNotEmpty) {
      debugPrint('⚠️ WikiHierarchy: Показываем ошибку с контентом');
      return _buildErrorWithContent();
    }

    // Если папок нет вообще (и ошибка есть) — только тогда пустой экран с ошибкой
    if (widget.folders.isEmpty) {
      debugPrint(
        '📝 WikiHierarchy: Показываем пустое состояние. Error: ${widget.error}',
      );
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withAlpha(120),
            ),
            const SizedBox(height: 16),
            Text(
              widget.error ?? 'Нет данных Wiki',
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Стандартный вывод - дерево папок и файлов
    debugPrint(
      '📁 WikiHierarchy: Показываем дерево папок (${widget.folders.length} папок)',
    );
    return _buildFolderTree();
  }

  /// Строит сообщение о пустых результатах поиска
  Widget _buildEmptySearchResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            LucideIcons.search,
            size: 48,
            color: Theme.of(context).colorScheme.onSurface.withAlpha(120),
          ),
          const SizedBox(height: 16),
          Text('По запросу ничего не найдено', textAlign: TextAlign.center),
        ],
      ),
    );
  }

  /// Строит сообщение об ошибке вместе с содержимым
  Widget _buildErrorWithContent() {
    return Column(
      children: [
        // Баннер ошибки
        Container(
          width: double.infinity,
          color: Theme.of(context).colorScheme.error.withAlpha(30),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Text(
            widget.error!,
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        // Дерево папок
        Expanded(child: _buildFolderTree()),
      ],
    );
  }

  /// Строит дерево папок и файлов
  Widget _buildFolderTree() {
    if (widget.folders.isEmpty) {
      return Center(child: Text(AppText.wikiEmptyState));
    }

    // Автоматически скроллим к выбранному элементу при изменении
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToSelectedFile();
    });

    // Создаем список корневых папок (оптимизированная версия)
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingM,
        vertical: AppSizes.paddingS,
      ),
      itemCount: widget.folders.length,
      // Добавляем cacheExtent для лучшей производительности
      cacheExtent: 1000,
      // Добавляем addAutomaticKeepAlives для сохранения состояния
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          key: ValueKey('folder_${widget.folders[index].id}'),
          child: _buildFolderItem(widget.folders[index], 0),
        );
      },
    );
  }

  /// Строит элемент папки с вложенными элементами
  /// [folder] - папка для отображения
  /// [level] - уровень вложенности для отступов
  Widget _buildFolderItem(WikiFolder folder, int level) {
    final iconSize = AppSizes.navIconSize * AppSizes.scaleSmall;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Заголовок папки с иконкой
        InkWell(
          onTap: () => widget.onToggleFolder(folder.id),
          child: Padding(
            padding: EdgeInsets.only(
              left: AppSizes.paddingS + level * AppSizes.paddingM,
              right: AppSizes.paddingS,
              top: 4.0,
              bottom: 4.0,
            ),
            child: Row(
              children: [
                // Иконка папки (эмодзи или стандартная)
                _buildFolderIcon(folder, iconSize),
                SizedBox(width: AppSizes.paddingM),
                // Название папки
                Expanded(
                  child: Text(
                    folder.name,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Вложенные элементы (если папка развернута)
        if (widget.allExpanded ||
            widget.expandedFolders.contains(folder.id)) ...[
          // Убираем анимацию для устранения проблем с производительностью
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Вложенные папки
              ...folder.subfolders.map(
                (subfolder) => _buildFolderItem(subfolder, level + 1),
              ),
              // Вложенные файлы - используем оптимизированный виджет
              ...folder.files.map(
                (file) => _WikiFileItem(
                  key: _getKeyForFile(file.id),
                  file: file,
                  level: level,
                  selectedFileId: widget.selectedFileId,
                  onFileTap: widget.onFileTap,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Строит иконку для папки (эмодзи или стандартную)
  Widget _buildFolderIcon(WikiFolder folder, double iconSize) {
    // Проверяем наличие пользовательских иконок
    bool hasCustomIcons =
        (folder.icon0 != null && folder.icon0!.isNotEmpty) &&
        (folder.icon1 != null && folder.icon1!.isNotEmpty);
    bool isOpen =
        widget.allExpanded || widget.expandedFolders.contains(folder.id);

    if (hasCustomIcons) {
      // Показываем icon0 для закрытой, icon1 для открытой папки
      return Text(
        isOpen ? folder.icon1! : folder.icon0!,
        style: TextStyle(fontSize: iconSize),
      );
    } else {
      // Стандартная иконка папки
      return Icon(
        isOpen ? Icons.folder_open : Icons.folder,
        size: iconSize,
        color: Theme.of(context).colorScheme.primary,
      );
    }
  }
}

/// Оптимизированный виджет для отображения файла в Wiki иерархии
/// Не перестраивается при изменениях структуры папок, только при изменении выделения
class _WikiFileItem extends StatelessWidget {
  final WikiFile file;
  final int level;
  final String? selectedFileId;
  final ValueChanged<WikiFile> onFileTap;

  const _WikiFileItem({
    super.key,
    required this.file,
    required this.level,
    required this.selectedFileId,
    required this.onFileTap,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize = AppSizes.navIconSize * AppSizes.scaleSmall;
    final theme = Theme.of(context);
    final bool isSelected = file.id == selectedFileId;

    return DrawerListItem(
      title: file.name,
      level: level + 1,
      leading: _buildFileIcon(isSelected, iconSize, theme),
      isActive: isSelected,
      activeColor: theme.colorScheme.primaryContainer.withAlpha(76),
      onTap: () => onFileTap(file),
    );
  }

  /// Строит иконку для файла (с индикатором выбора для выбранного файла)
  Widget _buildFileIcon(bool isSelected, double iconSize, ThemeData theme) {
    if (isSelected) {
      // Для выбранного файла показываем точку-индикатор и иконку
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            LucideIcons.dot,
            color: theme.colorScheme.primary,
            size: iconSize * 0.7,
          ),
          const SizedBox(width: 2),
          Icon(
            LucideIcons.fileText,
            size: iconSize,
            color: theme.colorScheme.primary,
          ),
        ],
      );
    } else {
      // Стандартная иконка файла
      return Icon(
        LucideIcons.fileText,
        size: iconSize,
        color: theme.colorScheme.primary,
      );
    }
  }
}
