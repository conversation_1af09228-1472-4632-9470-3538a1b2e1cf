import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:lucide_icons/lucide_icons.dart';
import 'package:luxury_app/app_provider.dart';
import 'package:luxury_app/app_providers.dart';
import 'package:luxury_app/app_state.dart';
import 'package:luxury_app/features/ai_chat/chat_list/chat_list_widget.dart';
import 'package:luxury_app/features/drawer/app_logo.dart';
import 'package:luxury_app/features/drawer/drawer_footer.dart';
import 'package:luxury_app/features/drawer/wiki_hierarchy_manager.dart';
import 'package:luxury_app/features/users/users_list_screen.dart';
import 'package:luxury_app/features/wiki/wiki_data/wiki_models.dart'
    as wiki_models;
import 'package:luxury_app/navigation/route_config.dart';
import 'package:luxury_app/shared/widgets/offline_indicator.dart';
import 'package:luxury_app/shared/widgets/sync_status_indicator.dart';

import '../../shared/constants/sizes.dart';
import '../../shared/constants/strings.dart';

// --- Класс DrawerService удален ---
// Логика создания AIChatBloc перенесена в MultiBlocProvider
// Ширина вычисляется напрямую в виджете

// DrawerContentManager и бизнес-логику вынесено в bloc/drawer_content_manager.dart

/// Отдельный виджет для бокового меню
class AppDrawer extends ConsumerStatefulWidget {
  /// Ключ Scaffold
  final GlobalKey<ScaffoldState> scaffoldKey;

  /// Скруглять ли углы (только для мобильного Drawer)
  final bool roundedCorners;

  /// Создает виджет бокового меню
  const AppDrawer({
    super.key,
    required this.scaffoldKey,
    this.roundedCorners = true,
  });

  @override
  ConsumerState<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends ConsumerState<AppDrawer> {
  late final PageController _pageController;

  // Кешируем виджеты страниц чтобы избежать пересоздания
  late final Widget _wikiPageWidget;
  late final Widget _aiPageWidget;
  late final Widget _usersPageWidget;

  @override
  void initState() {
    super.initState();
    final initialMode = ref.read(appProvider).drawerMode;
    final initialPage = _getPageIndexFromMode(initialMode);
    _pageController = PageController(initialPage: initialPage);

    // Создаем виджеты страниц один раз (кэширование)
    _wikiPageWidget = _createWikiPage();
    _aiPageWidget = _createAiPage();
    _usersPageWidget = _createUsersPage();
  }

  int _getPageIndexFromMode(DrawerMode mode) {
    switch (mode) {
      case DrawerMode.wiki:
        return 0;
      case DrawerMode.chat:
        return 1;
      case DrawerMode.users:
        return 2;
      case DrawerMode.news:
        return 0; // Для news используем wiki страницу
    }
  }

  DrawerMode _getModeFromPageIndex(int index) {
    switch (index) {
      case 0:
        return DrawerMode.wiki;
      case 1:
        return DrawerMode.chat;
      case 2:
        return DrawerMode.users;
      default:
        return DrawerMode.wiki;
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final bool isDesktop = screenWidth >= AppSizes.mobileBreakpoint;
    final drawerWidth = isDesktop ? 340.0 : screenWidth * 0.85;
    final BorderRadiusGeometry borderRadius =
        (!isDesktop && widget.roundedCorners)
            ? const BorderRadius.only(
              topRight: Radius.circular(24),
              bottomRight: Radius.circular(24),
            )
            : BorderRadius.zero;

    return Consumer(
      builder: (context, ref, child) {
        final state = ref.watch(appProvider);

        // Слушаем изменения режима drawer
        ref.listen<DrawerMode>(
          appProvider.select((state) => state.drawerMode),
          (previous, current) {
            if (previous != current) {
              final page = _getPageIndexFromMode(current);
              if (_pageController.hasClients &&
                  _pageController.page?.round() != page) {
                _pageController.animateToPage(
                  page,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.ease,
                );
              }
            }
          },
        );

        // Для desktop версии используем простой контейнер без Drawer wrapper
        if (!isDesktop) {
          return ClipRRect(
            borderRadius: borderRadius,
            child: Drawer(
              width: drawerWidth,
              child: SafeArea(child: _buildDrawerContent(state)),
            ),
          );
        } else {
          return SafeArea(child: _buildDrawerContent(state));
        }
      },
    );
  }

  // Создает Wiki страницу один раз
  Widget _createWikiPage() {
    return Consumer(
      builder: (context, ref, child) {
        final appState = ref.watch(appProvider);
        final selectedFileId =
            appState.activeScreen == DrawerMode.wiki
                ? appState.lastRequestedPageId
                : null;

        return WikiHierarchyManager(
          selectedFileId: selectedFileId,
          onFileTap: (wiki_models.WikiFile file) {
            ref.read(appProvider.notifier).navigateToWiki(file.id);

            if (MediaQuery.of(context).size.width < 680) {
              widget.scaffoldKey.currentState?.closeDrawer();
            }
            context.goNamed(
              RouteConfig.wikiRoute,
              pathParameters: {'pageId': file.id},
              queryParameters: {'name': file.name},
            );
          },
        );
      },
    );
  }

  // Создает AI страницу один раз
  Widget _createAiPage() {
    return ChatListWidget(scaffoldKey: widget.scaffoldKey);
  }

  // Создает страницу пользователей один раз
  Widget _createUsersPage() {
    return UsersListScreen(scaffoldKey: widget.scaffoldKey);
  }

  // Построение содержимого drawer
  Widget _buildDrawerContent(AppState state) {
    return Column(
      children: [
        AppDrawerHeader(context: context, state: state),
        const Divider(height: 0, thickness: 1),
        Expanded(
          child: PageView(
            controller: _pageController,
            physics: const ClampingScrollPhysics(),
            onPageChanged: (index) {
              final mode = _getModeFromPageIndex(index);
              if (mode != state.drawerMode) {
                ref.read(appProvider.notifier).changeDrawerMode(mode);
              }
            },
            children: [_wikiPageWidget, _aiPageWidget, _usersPageWidget],
          ),
        ),
        const Divider(height: 0, thickness: 1),
        DrawerFooter(scaffoldKey: widget.scaffoldKey, context: context),
      ],
    );
  }

  @override
  void didUpdateWidget(covariant AppDrawer oldWidget) {
    super.didUpdateWidget(oldWidget);
    // ничего
  }
}

/// Виджет для отображения заголовка боковой панели
class AppDrawerHeader extends ConsumerWidget {
  /// Контекст приложения
  final BuildContext context;

  /// Текущее состояние приложения
  final AppState state;

  /// Создает заголовок боковой панели
  const AppDrawerHeader({
    super.key,
    required this.context,
    required this.state,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      height: 80.0,
      padding: const EdgeInsets.symmetric(
        horizontal: AppSizes.paddingS,
        vertical: AppSizes.paddingXS,
      ),
      child: Row(
        children: [
          Expanded(child: _buildModeToggle(ref)),
          const SizedBox(width: 8.0),
          const SyncStatusIndicator(),
          const SizedBox(width: 4.0),
          const OfflineIndicatorCompact(),
          const SizedBox(width: 8.0),
          const AppLogo(),
        ],
      ),
    );
  }

  /// Создает переключатель режимов боковой панели
  Widget _buildModeToggle(WidgetRef ref) {
    final permissionService = ref.watch(permissionServiceProvider);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildModeButton(
            icon: LucideIcons.library,
            label: AppText.wikiTitle,
            tooltip: AppText.wikiTitle,
            isSelected: state.drawerMode == DrawerMode.wiki,
            onTap:
                () => ref
                    .read(appProvider.notifier)
                    .changeDrawerMode(DrawerMode.wiki),
          ),
          const SizedBox(width: 12.0),
          _buildModeButton(
            icon: LucideIcons.brain,
            label: AppText.aiTitle,
            tooltip: AppText.aiTitle,
            isSelected: state.drawerMode == DrawerMode.chat,
            onTap:
                () => ref
                    .read(appProvider.notifier)
                    .changeDrawerMode(DrawerMode.chat),
          ),
          // Показываем пункт "Пользователи" только админам
          if (permissionService.isCurrentUserAdmin) ...[
            const SizedBox(width: 12.0),
            _buildModeButton(
              icon: LucideIcons.users,
              label: 'Пользователи',
              tooltip: 'Управление пользователями',
              isSelected: state.drawerMode == DrawerMode.users,
              onTap:
                  () => ref
                      .read(appProvider.notifier)
                      .changeDrawerMode(DrawerMode.users),
            ),
          ],
        ],
      ),
    );
  }

  /// Создает кнопку переключения режима боковой панели
  Widget _buildModeButton({
    required IconData icon,
    required String label,
    required String tooltip,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final baseColor = colorScheme.onSurface.withAlpha(178);

    final Color iconColor = isSelected ? colorScheme.primary : baseColor;

    final TextStyle labelStyle = TextStyle(
      color: isSelected ? colorScheme.primary : baseColor,
      fontSize: 12.0,
      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
    );

    final BoxDecoration containerDecoration = BoxDecoration(
      color:
          isSelected ? colorScheme.primary.withAlpha(25) : Colors.transparent,
      borderRadius: BorderRadius.circular(8.0),
    );

    return Tooltip(
      message: tooltip,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(8.0),
              child: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: containerDecoration,
                child: Icon(icon, color: iconColor, size: 24.0),
              ),
            ),
          ),
          const SizedBox(height: 4.0),
          Text(label, style: labelStyle),
        ],
      ),
    );
  }
}
