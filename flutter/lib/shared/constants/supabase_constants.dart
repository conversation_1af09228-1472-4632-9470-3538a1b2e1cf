/// Константы для Supabase
class SupabaseConstants {
  // Локальные значения для разработки
  static const String localUrl = 'http://127.0.0.1:54321';
  static const String localAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0';

  // Продакшн значения
  static const String prodUrl = 'https://namplnzyrvdvskwnzpcu.supabase.co';
  static const String prodAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5hbXBsbnp5cnZkdnNrd256cGN1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwOTg0OTAsImV4cCI6MjA2NzY3NDQ5MH0._clVkHlCZao0tAPLbvAd0xRfNtvhAtg3HgxVaCjMvCg';

  // Флаг для переключения между локальным и продакшн окружением
  // true = продакшн, false = локальная разработка
  static const bool useProduction = true;

  // Текущие значения (автоматически выбираются на основе флага)
  static String get url => useProduction ? prodUrl : localUrl;
  static String get anonKey => useProduction ? prodAnonKey : localAnonKey;

  // Информация о текущем окружении
  static String get environment =>
      useProduction ? 'Production' : 'Local Development';
}
