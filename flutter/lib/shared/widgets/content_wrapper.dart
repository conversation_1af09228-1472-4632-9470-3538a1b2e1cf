import 'package:flutter/material.dart';

/// Виджет-обертка для предотвращения перерисовки контента
/// 
/// Этот виджет изолирует свое содержимое от перестроений родительских виджетов.
/// Используется для оптимизации производительности, когда необходимо
/// предотвратить перерисовку контента при изменении состояния родительских виджетов.
class ContentWrapper extends StatefulWidget {
  /// Ключ для идентификации содержимого
  final Key? contentKey;
  
  /// Содержимое, которое нужно изолировать от перестроений
  final Widget child;

  const ContentWrapper({
    super.key,
    this.contentKey,
    required this.child,
  });

  @override
  State<ContentWrapper> createState() => _ContentWrapperState();
}

class _ContentWrapperState extends State<ContentWrapper> {
  /// Используем ключ для хранения состояния в PageStorage
  late final Key _contentKey;
  
  @override
  void initState() {
    super.initState();
    // Используем переданный ключ или создаем уникальный
    _contentKey = widget.contentKey ?? ValueKey('content-${widget.hashCode}');
  }

  @override
  Widget build(BuildContext context) {
    // Используем RepaintBoundary для предотвращения перерисовки содержимого (оптимизированная версия)
    final isDesktop = MediaQuery.of(context).size.width >= 600;
    
    return RepaintBoundary(
      child: KeyedSubtree(
        key: _contentKey,
        child: isDesktop
            ? ColoredBox(
                color: Theme.of(context).scaffoldBackgroundColor,
                child: widget.child,
              )
            : widget.child,
      ),
    );
  }
}
