import 'dart:async';

import 'package:flutter/material.dart';
import 'package:luxury_app/shared/widgets/markdown_parser.dart';
import 'package:luxury_app/shared/widgets/text_skeleton.dart';

/// Единый рендерер markdown с поддержкой Mermaid, видео
class MarkdownRenderer extends StatefulWidget {
  final String data;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onAllImagesLoaded;

  const MarkdownRenderer({
    super.key,
    required this.data,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
    this.padding,
    this.onAllImagesLoaded,
  });

  @override
  State<MarkdownRenderer> createState() => _MarkdownRendererState();
}

class _MarkdownRendererState extends State<MarkdownRenderer> {
  List<Widget>? _parsed;
  String? _lastData;
  bool _parsing = false;
  Timer? _debounceTimer;

  // Для отслеживания текущей операции парсинга
  String? _currentParsingData;
  Completer<void>? _currentParsingCompleter;

  int _imageCount = 0;
  int _loadedImages = 0;
  bool _callbackCalled = false;

  // Debounce для обновлений
  static const Duration _debounceDelay = Duration(milliseconds: 150);

  void _onImageLoaded() {
    if (!mounted) return;

    _loadedImages++;
    if (!_callbackCalled && _loadedImages >= _imageCount) {
      _callbackCalled = true;
      widget.onAllImagesLoaded?.call();
    }
  }

  @override
  void initState() {
    super.initState();
    _parseAsync();
  }

  @override
  void didUpdateWidget(covariant MarkdownRenderer oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Проверяем изменения в данных
    if (widget.data != _lastData) {
      _scheduleUpdate();
    }
  }

  void _scheduleUpdate() {
    _debounceTimer?.cancel();

    _debounceTimer = Timer(_debounceDelay, () {
      if (mounted && !_parsing && widget.data != _lastData) {
        _parseAsync();
      }
    });
  }

  Future<void> _parseAsync() async {
    // Отменяем предыдущую операцию парсинга если она еще выполняется
    if (_parsing && _currentParsingCompleter != null) {
      _currentParsingCompleter!.complete();
      _currentParsingCompleter = null;
    }

    // Проверяем нужно ли обновлять
    if (_lastData == widget.data) {
      return;
    }

    final dataToProcess = widget.data;
    _currentParsingData = dataToProcess;
    _currentParsingCompleter = Completer<void>();

    setState(() {
      _parsing = true;
      _lastData = dataToProcess;
      _imageCount = 0;
      _loadedImages = 0;
      _callbackCalled = false;
    });

    try {
      // Парсим через основной MarkdownParser
      final parsed = await parseMarkdownWidgets(
        dataToProcess,
        countImages: true,
      );

      // Проверяем, что данные все еще актуальны и компонент не был уничтожен
      if (!mounted || _currentParsingData != dataToProcess) {
        return;
      }

      setState(() {
        _parsed = parsed.widgets;
        _imageCount = parsed.imageCount;
        _parsing = false;
      });

      if (_imageCount == 0 && !_callbackCalled) {
        _callbackCalled = true;
        widget.onAllImagesLoaded?.call();
      }
    } catch (e) {
      // Проверяем, что компонент не был уничтожен
      if (!mounted || _currentParsingData != dataToProcess) {
        return;
      }

      // Fallback к синхронному парсингу при ошибке
      final parsed = parseMarkdownWidgetsWithImageCount(dataToProcess);
      setState(() {
        _parsed = parsed.widgets;
        _imageCount = parsed.imageCount;
        _parsing = false;
      });

      if (_imageCount == 0 && !_callbackCalled) {
        _callbackCalled = true;
        widget.onAllImagesLoaded?.call();
      }
    } finally {
      _currentParsingCompleter?.complete();
      _currentParsingCompleter = null;
      _currentParsingData = null;
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();

    // Отменяем текущую операцию парсинга
    if (_currentParsingCompleter != null) {
      _currentParsingCompleter!.complete();
      _currentParsingCompleter = null;
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_parsing || _parsed == null) {
      return ListView.builder(
        shrinkWrap: widget.shrinkWrap,
        physics: widget.physics,
        padding: widget.padding ?? EdgeInsets.zero,
        itemCount: 12,
        itemBuilder: (_, i) => const TextSkeleton(),
      );
    }

    final children =
        _parsed!.map((w) {
          if (w is ImageWithCallback) {
            return w.buildWithCallback(_onImageLoaded);
          } else if (w is VideoWithCallback) {
            return w.buildWithCallback(_onImageLoaded);
          }
          return w;
        }).toList();

    return ListView(
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      padding: widget.padding ?? EdgeInsets.zero,
      children: children,
    );
  }
}
