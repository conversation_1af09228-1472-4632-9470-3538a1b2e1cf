import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class KeychainHelper {
  static final _storage = const FlutterSecureStorage();

  static Future<void> savePassword(String email, String password) async {
    await _storage.write(key: 'luxuryapp_password_$email', value: password);
  }

  static Future<String?> getPassword(String email) async {
    return await _storage.read(key: 'luxuryapp_password_$email');
  }

  static Future<void> deletePassword(String email) async {
    await _storage.delete(key: 'luxuryapp_password_$email');
  }
}
