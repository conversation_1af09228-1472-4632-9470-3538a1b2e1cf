import 'dart:math';

class PasswordGenerator {
  static const _lowercase = 'abcdefghijklmnopqrstuvwxyz';
  static const _uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  static const _numbers = '0123456789';
  static const _symbols = '!@#%^&*()-_=+[]{};:,.<>?';

  static String generate({int length = 16, bool withSymbols = true}) {
    final chars = _lowercase + _uppercase + _numbers + (withSymbols ? _symbols : '');
    final rand = Random.secure();
    return List.generate(length, (_) => chars[rand.nextInt(chars.length)]).join();
  }
}
