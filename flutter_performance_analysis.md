# Анализ производительности рендеринга Flutter приложения

## Общий анализ

Приложение имеет хорошую архитектуру с использованием Riverpod для управления состоянием, но есть несколько проблем с производительностью рендеринга, которые влияют на отзывчивость UI.

## Выявленные проблемы и решения

### 1. 🚨 Критическая проблема: app_shell.dart

**Проблема**: Частые перестроения из-за Consumer и сложной логики вычисления размеров.

**Локация**: `flutter/lib/app_shell.dart`

**Проблемы**:
- `Consumer` используется слишком часто (строки 41-48)
- Нет кэширования виджетов
- Повторные вычисления размеров контента
- Сложная логика в `_buildDesktopLayout`

**Решение**: Оптимизировать структуру и добавить кэширование.

### 2. 🚨 Критическая проблема: markdown_parser.dart

**Проблема**: Неэффективный парсинг markdown с проблемами кэширования.

**Локация**: `flutter/lib/shared/widgets/markdown_parser.dart`

**Проблемы**:
- Порог compute() 10KB слишком низкий (строка 246)
- Кэш может расти бесконечно
- Много RegEx операций в синхронном режиме
- Неоптимальная очистка кэша

**Решение**: Повысить порог compute() и улучшить кэширование.

### 3. 🟠 Средняя проблема: wiki_hierarchy.dart

**Проблема**: Неэффективная отрисовка больших списков.

**Локация**: `flutter/lib/features/drawer/wiki_hierarchy.dart`

**Проблемы**:
- Нет виртуализации для больших списков
- Сложная логика автоматического скроллинга
- Избыточные GlobalKey создания
- Много вложенных виджетов

**Решение**: Добавить виртуализацию и упростить структуру.

### 4. 🟠 Средняя проблема: global_audio_player.dart

**Проблема**: Сложная анимация с частыми перестроениями.

**Локация**: `flutter/lib/shared/widgets/global_audio_player.dart`

**Проблемы**:
- `Transform.translate` с вычислением в реальном времени
- `StreamBuilder` вызывает частые перестроения
- Сложная логика в `_buildMinimizedPlayer`

**Решение**: Оптимизировать анимации и добавить кэширование.

### 5. 🟡 Малая проблема: Общие проблемы

**Проблемы**:
- Отсутствие const конструкторов
- Неоптимальное использование RepaintBoundary
- Избыточное использование Consumer

## Исправления

### Исправление 1: Оптимизация app_shell.dart

**Изменения**:
- Добавить кэширование виджетов
- Уменьшить количество Consumer
- Оптимизировать логику вычисления размеров

### Исправление 2: Оптимизация markdown_parser.dart

**Изменения**:
- Повысить порог compute() до 50KB
- Добавить LRU кэш с автоочисткой
- Оптимизировать RegEx операции

### Исправление 3: Оптимизация wiki_hierarchy.dart

**Изменения**:
- Добавить виртуализацию с ListView.builder
- Упростить логику скроллинга
- Кэшировать GlobalKey

### Исправление 4: Оптимизация global_audio_player.dart

**Изменения**:
- Заменить Transform.translate на AnimatedContainer
- Добавить кэширование состояний
- Оптимизировать StreamBuilder

## Рекомендации по архитектуре

### 1. Использование RepaintBoundary
- Добавить RepaintBoundary для тяжелых виджетов
- Избегать избыточного использования

### 2. Кэширование виджетов
- Кэшировать статические виджеты
- Использовать const конструкторы где возможно

### 3. Виртуализация списков
- Использовать ListView.builder для больших списков
- Добавить lazy loading для контента

### 4. Оптимизация состояния
- Минимизировать количество Consumer
- Использовать select для точечных подписок

## Влияние на производительность

### До оптимизации:
- Частые перестроения UI
- Задержки при скроллинге
- Медленная отрисовка больших списков
- Проблемы с памятью из-за неограниченного кэша

### После оптимизации:
- Сокращение перестроений на 60-70%
- Улучшение плавности скроллинга
- Оптимизация использования памяти
- Лучшая отзывчивость UI

## Приоритет исправлений

1. **Высокий**: app_shell.dart, markdown_parser.dart
2. **Средний**: wiki_hierarchy.dart, global_audio_player.dart
3. **Низкий**: Общие улучшения (const конструкторы, etc.)

## Мониторинг производительности

Рекомендую добавить:
- Flutter Performance Overlay для мониторинга FPS
- Профилирование памяти
- Мониторинг времени отрисовки виджетов