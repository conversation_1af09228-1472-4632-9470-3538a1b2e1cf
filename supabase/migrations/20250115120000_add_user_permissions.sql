-- Add user permissions system
-- Migration for user roles and permissions

-- Create user_permissions table
create table user_permissions (
    id uuid primary key default uuid_generate_v4(),
    user_id uuid not null references auth.users(id) on delete cascade,
    permission_name text not null,
    granted_by_user_id uuid not null references auth.users(id),
    created_at timestamptz default now() not null,
    
    -- Ensure unique permission per user
    unique(user_id, permission_name)
);

-- Create indexes for performance
create index idx_user_permissions_user_id on user_permissions(user_id);
create index idx_user_permissions_permission_name on user_permissions(permission_name);

-- Enable RLS
alter table user_permissions enable row level security;

-- RLS policies for user_permissions
-- Only admins can manage permissions
create policy "<PERSON><PERSON> can manage all permissions" on user_permissions
    for all using (
        exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

-- Users can read their own permissions
create policy "Users can read their own permissions" on user_permissions
    for select using (user_id = auth.uid());

-- Update existing RLS policies for content management
-- NEWS: Allow users with permissions to manage news
create policy "Users with permissions can create news" on news
    for insert with check (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_create_news'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can update news" on news
    for update using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_edit_news'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can delete news" on news
    for delete using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_delete_news'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

-- WIKI: Allow users with permissions to manage wiki
create policy "Users with permissions can create wiki folders" on wiki_folders
    for insert with check (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_create_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can update wiki folders" on wiki_folders
    for update using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_edit_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can delete wiki folders" on wiki_folders
    for delete using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_delete_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can create wiki files" on wiki_files
    for insert with check (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_create_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can update wiki files" on wiki_files
    for update using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_edit_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

create policy "Users with permissions can delete wiki files" on wiki_files
    for delete using (
        exists (
            select 1 from user_permissions 
            where user_id = auth.uid() 
            and permission_name = 'can_delete_wiki'
        )
        or exists (
            select 1 from auth.users 
            where id = auth.uid() 
            and raw_user_meta_data->>'role' = 'admin'
        )
    );

-- Set admin role for the specified user
update auth.users 
set raw_user_meta_data = jsonb_set(
    coalesce(raw_user_meta_data, '{}'),
    '{role}',
    '"admin"'
)
where email = '<EMAIL>';

-- Enable realtime for user_permissions table
alter publication supabase_realtime add table user_permissions;

-- Create RPC function to get all users (for admin use)
create or replace function get_all_users()
returns table (
    id uuid,
    email text,
    phone text,
    role text,
    created_at timestamptz,
    metadata jsonb
)
security definer
language plpgsql
as $$
begin
    -- Check if current user is admin
    if not exists (
        select 1 from auth.users 
        where auth.users.id = auth.uid() 
        and raw_user_meta_data->>'role' = 'admin'
    ) then
        raise exception 'Недостаточно прав для просмотра пользователей';
    end if;

    return query
    select 
        auth.users.id,
        auth.users.email,
        auth.users.phone,
        coalesce(auth.users.raw_user_meta_data->>'role', 'user') as role,
        auth.users.created_at,
        auth.users.raw_user_meta_data as metadata
    from auth.users
    order by auth.users.created_at desc;
end;
$$; 